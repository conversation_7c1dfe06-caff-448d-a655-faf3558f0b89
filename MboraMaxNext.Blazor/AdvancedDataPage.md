# Advanced Data Page Guide

This guide explains the advanced features available for creating rich, interactive data pages in the MboraMax application. It covers filtering, templates in data grids, and dashboard components.

## Table of Contents

1. [Data Grid Features](#data-grid-features)
   - [Column Templates](#column-templates)
   - [Filtering](#filtering)
   - [Sorting](#sorting)
   - [Copy to Clipboard](#copy-to-clipboard)
   - [Formatting Values](#formatting-values)
2. [Dashboard Components](#dashboard-components)
   - [HorizontalStatusChart](#horizontalstatuschart)
   - [DoughnutStatsChart](#doughnutstatschart)
3. [Best Practices](#best-practices)

## Data Grid Features

### Column Templates

The Blazorise DataGrid component allows for custom column templates using the `DisplayTemplate` tag:

```razor
<DataGridColumn Field="@nameof(MemberDto.MboraId)" Caption="@L["MboraId"]">
    <DisplayTemplate>
        <div class="d-flex align-items-center">
            <span>@context.MboraId</span>
            <Button Color="Color.Light" Size="Size.Small" Margin="Margin.Is1.FromStart"
                    @onclick="() => CopyToClipboard(context.MboraId.ToString())">
                <Icon Name="IconName.Copy" Size="IconSize.Small"/>
            </Button>
        </div>
    </DisplayTemplate>
</DataGridColumn>
```

### Filtering

#### Basic Filtering

For basic text filtering, use the `SearchText` property of the DataGridHeader component:

```razor
<DataGridHeader
    SearchText="@GetListInput.Filter"
    OnSearchChanged="@OnSearchTextChanged"
    ...>
</DataGridHeader>
```

#### Advanced Filtering with Multiple Criteria

For more complex filtering, such as filtering by multiple entities:

1. Create a HashSet to store selected filter IDs:
```csharp
private readonly HashSet<Guid> _selectedOccupationIds = [];
```

2. Create methods to handle selection changes:
```csharp
private async Task OnOccupationSelectionChanged(Guid occupationId, bool selected)
{
    if (selected)
    {
        _selectedOccupationIds.Add(occupationId);
    }
    else
    {
        _selectedOccupationIds.Remove(occupationId);
    }
    
    // Update filter input
    GetListInput.FilterByOccupations = _selectedOccupationIds.Count > 0;
    GetListInput.OccupationIds = _selectedOccupationIds.ToList();
    
    // Refresh data
    await SearchEntitiesAsync();
}
```

3. Create helper methods for checking selection status:
```csharp
private bool IsAllOccupationsSelected => _selectedOccupationIds.Count == TopOccupations.Count;
private bool IsOccupationSelected(Guid occupationId) => _selectedOccupationIds.Contains(occupationId);
```

### Sorting

To enable sorting in the DataGrid:

1. Make sure your column has `Sortable` set to true:
```razor
<DataGridColumn Field="@nameof(MemberDto.Temporary)" Caption="@L["Status"]" Sortable>
```

2. In your repository, implement special handling for complex sorting cases:
```csharp
public IQueryable<Member> ApplySorting(IQueryable<Member> query, string sorting)
{
    if (!string.IsNullOrWhiteSpace(sorting))
    {
        // Handle special cases for related properties
        if (sorting.Contains("Temporary", StringComparison.OrdinalIgnoreCase))
        {
            var isDescending = sorting.EndsWith(" DESC", StringComparison.OrdinalIgnoreCase);
            query = isDescending
                ? query.OrderByDescending(m => m.Temporary)
                : query.OrderBy(m => m.Temporary);
        }
        else
        {
            query = query.OrderBy(sorting);
        }
    }
    return query;
}
```

### Copy to Clipboard

Implement a copy-to-clipboard feature for fields that users might need to copy:

```csharp
private async Task CopyToClipboard(string text)
{
    await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
    await JSRuntime.InvokeVoidAsync("alert", L["CopiedToClipboard"].Value);
}
```

Usage in a column template:
```razor
<Button Color="Color.Light" Size="Size.Small" Margin="Margin.Is1.FromStart"
        @onclick="() => CopyToClipboard(context.AgentCode)">
    <Icon Name="IconName.Copy" Size="IconSize.Small"/>
</Button>
```

### Formatting Values

Use badges and formatting to make values more readable:

```razor
<DataGridColumn Field="@nameof(AgentDto.MaximumTransactionAmount)" Caption="@L["MaxTransAmount"]">
    <DisplayTemplate>
        <Badge Color="Color.Light" TextColor="TextColor.Dark">
            MWK @(context.MaximumTransactionAmount?.ToString("N2") ?? "0.00")
        </Badge>
    </DisplayTemplate>
</DataGridColumn>
```

For status indicators:
```razor
<Badge Color="@(context.Temporary ? Color.Warning : Color.Success)">
    @(context.Temporary ? L["Temporary"] : L["Permanent"])
</Badge>
```

## Dashboard Components

### HorizontalStatusChart

The `HorizontalStatusChart` component displays horizontal progress bars with labels and values. It's used for showing member statistics by gender, status, etc.

```razor
<HorizontalStatusChart 
    Title="@L["MembersByGender"]"
    Labels="@(new[] { L["Male"].Value, L["Female"].Value })"
    Values="@(new[] { Statistics.MalePercentage, Statistics.FemalePercentage })"
    Colors="@(new[] { "success", "primary" })"
    ShowPercentages="true" />
```

### DoughnutStatsChart

The `DoughnutStatsChart` component displays a doughnut chart with customizable labels, values, and a center text. It's used for showing occupation statistics.

```razor
<DoughnutStatsChart 
    Title="@L["OccupationDistribution"]"
    Labels="@Statistics.OccupationLabels"
    Values="@Statistics.OccupationPercentages"
    CenterText="@L["Total"].Value"
    CenterValue="@Statistics.TotalMembers.ToString()" />
```

## Best Practices

1. **Repository Pattern**: Use the repository pattern to separate data access logic from application services. This makes your code more maintainable and testable.

2. **Reusable Components**: Create reusable components for common UI elements like charts and specialized data grids to maintain consistency across the application.

3. **Localization**: Always use the localization service (`@L["Key"]`) for displaying text to support multiple languages.

4. **Responsive Design**: Ensure your data grids and dashboards are responsive by using appropriate Blazorise classes and components.

5. **Error Handling**: Implement proper error handling for data operations and display user-friendly error messages.

6. **Performance**: For large datasets, implement pagination and efficient filtering to maintain good performance.

7. **Consistent Styling**: Use consistent colors, badges, and formatting across the application to provide a cohesive user experience.
