using MboraMaxNext.Blazor.Menus;
using MboraMaxNext.Localization;
using MboraMaxNext.Permissions;
using Volo.Abp.UI.Navigation;

namespace MboraMaxNext.Menus;

public class MemberMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }
    }

    private static async Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var l = context.GetLocalizer<MboraMaxNextResource>();

        // Add Members menu item directly under the main menu
        var membersMenu = new ApplicationMenuItem(
            name: MboraMaxNextMenus.Members,
            displayName: l["Members"],
            url: "/members",
            icon: "fas fa-users-line",
            order: 4 // Position it after the dashboard
        );

        // Only show if user has permission to view members
        if (await context.IsGrantedAsync(MboraMaxNextPermissions.Members.Default))
        {
            context.Menu.AddItem(membersMenu);
        }
    }
}
