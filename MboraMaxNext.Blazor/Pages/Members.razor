@page "/members"
@namespace MboraMaxNext.Blazor.Pages.MembersList
@attribute [Authorize(MboraMaxNextPermissions.Members.Default)]
@using MboraMaxNext.Localization
@using MboraMaxNext.Permissions
@using MboraMaxNext.Services.Dtos.Administration.Members
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
@using MboraMaxNext.Services.Dtos.Administration.Villages
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using MboraMaxNext.Services.Administration
@using MboraMaxNext.Services.Dtos.Administration.Hubs
@using MboraMaxNext.Services.Dtos.Administration.Occupations
@using MboraMaxNext.Services.Dtos.Administration.Groups
@using MboraMaxNext.Entities.Administration
@using MboraMaxNext.Blazor.Components
@using MboraMaxNext.Models
@inject NavigationManager NavigationManager
@inject IStringLocalizer<MboraMaxNextResource> L
@inject IJSRuntime JsRuntime
@inject IMemberAppService AppService
@inherits AbpCrudPageBase<MboraMaxNext.Services.Administration.IMemberAppService, MemberDto, Guid, GetMembersInput, CreateUpdateMemberDto>

<PageTitle>@L["Members"]</PageTitle>

<Card>
    <CardHeader>
        <DataGridHeaderEx @ref="dataGridHeader"
                          Title="@L["Members"]"
                          HasCreatePermission="@HasCreatePermission"
                          HasExportPermission="@HasCreatePermission"
                          ShowDateRange="true"
                          DateRangeLabel="@L["RegistrationDateRange"]"
                          StartDate="@GetListInput.RegistrationStartDate"
                          EndDate="@GetListInput.RegistrationEndDate"
                          SearchText="@GetListInput.Filter"
                          Tabs="@Tabs"
                          DefaultTab="villages"
                          OnDateRangeChanged="@OnDateRangeChanged"
                          OnSearchChanged="@OnSearchChanged"
                          OnCreateClicked="@NavigateToCreatePage"
                          OnExportClicked="@ExportToExcelAsync"
                          OnFilterVisibilityChanged="@OnFilterVisibilityChanged"
                          OnFilterTabChanged="@OnFilterTabChanged">
            <Filters>
                <div class="villages-filter" style="display: @GetFilterTabDisplay("villages")">
                    <Field>
                        <Check TValue="bool"
                               Checked="@IsAllVillagesSelected"
                               CheckedChanged="@(async (value) => await OnSelectAllVillagesChanged(value))">
                            @L["All"]
                        </Check>
                    </Field>
                    <div style="max-height: 250px; overflow-y: auto;">
                        @foreach (var village in Villages)
                        {
                            <Field>
                                <Check TValue="bool"
                                       Checked="@IsVillageSelected(village.Id)"
                                       CheckedChanged="@(async (value) => await OnVillageSelectionChanged(village.Id, value))">
                                    @village.Name
                                </Check>
                            </Field>
                        }
                    </div>
                </div>

                <div class="hubs-filter" style="display: @GetFilterTabDisplay("hubs")">
                    <Field>
                        <Check TValue="bool"
                               Checked="@IsAllHubsSelected"
                               CheckedChanged="@(async (value) => await OnSelectAllHubsChanged(value))">
                            @L["All"]
                        </Check>
                    </Field>
                    <div style="max-height: 250px; overflow-y: auto;">
                        @foreach (var hub in Hubs)
                        {
                            <Field>
                                <Check TValue="bool"
                                       Checked="@IsHubSelected(hub.Id)"
                                       CheckedChanged="@(async (value) => await OnHubSelectionChanged(hub.Id, value))">
                                    @hub.HubName
                                </Check>
                            </Field>
                        }
                    </div>
                </div>

                <div class="occupations-filter" style="display: @GetFilterTabDisplay("occupations")">
                    <Field>
                        <Check TValue="bool"
                               Checked="@IsAllOccupationsSelected"
                               CheckedChanged="@(async (value) => await OnSelectAllOccupationsChanged(value))">
                            @L["All"]
                        </Check>
                    </Field>
                    <div style="max-height: 250px; overflow-y: auto;">
                        @foreach (var occupation in TopOccupations)
                        {
                            <Field>
                                <Check TValue="bool"
                                       Checked="@IsOccupationSelected(occupation.Id)"
                                       CheckedChanged="@(async (value) => await OnOccupationSelectionChanged(occupation.Id, value))">
                                    @occupation.Name
                                </Check>
                            </Field>
                        }
                    </div>
                </div>

                <div class="status-filter" style="display: @GetFilterTabDisplay("status")">
                    <Field>
                        <RadioGroup TValue="bool?" Name="status" CheckedValue="@GetListInput.IsTemporary"
                                    CheckedValueChanged="@OnStatusSelectionChanged">
                            <Radio TValue="bool?" Value="@(null)">@L["All"]</Radio>
                            <Radio TValue="bool?" Value="@(true)">@L["Temporary"]</Radio>
                            <Radio TValue="bool?" Value="@(false)">@L["Permanent"]</Radio>
                        </RadioGroup>
                    </Field>
                </div>

                <div class="gender-filter" style="display: @GetFilterTabDisplay("gender")">
                    <Field>
                        <Radio TValue="Gender?"
                               Name="gender"
                               Value="null"
                               Checked="GetListInput.Gender == null"
                               CheckedChanged="@(async (value) => await OnGenderSelectionChanged(null))">
                            @L["All"]
                        </Radio>
                    </Field>
                    <Field>
                        <Radio TValue="Gender?"
                               Name="gender"
                               Value="Gender.Male"
                               Checked="GetListInput.Gender == Gender.Male"
                               CheckedChanged="@(async (value) => await OnGenderSelectionChanged(Gender.Male))">
                            @L["Male"]
                        </Radio>
                    </Field>
                    <Field>
                        <Radio TValue="Gender?"
                               Name="gender"
                               Value="Gender.Female"
                               Checked="GetListInput.Gender == Gender.Female"
                               CheckedChanged="@(async (value) => await OnGenderSelectionChanged(Gender.Female))">
                            @L["Female"]
                        </Radio>
                    </Field>
                </div>
            </Filters>
        </DataGridHeaderEx>

        <Row Class="mt-2">
            <Column>
                @if (_selectedVillageIds.Count > 0 && !IsAllVillagesSelected)
                {
                    @foreach (var villageId in _selectedVillageIds)
                    {
                        var village = Villages.FirstOrDefault(v => v.Id == villageId);
                        if (village != null)
                        {
                            <Badge Color="Color.Secondary" Class="me-2 mb-2"
                                   CloseClicked="@(async () => await OnVillageSelectionChanged(villageId, false))">
                                @($"{village.Name}")
                            </Badge>
                        }
                    }
                }
                @if (_selectedHubIds.Count > 0 && !IsAllHubsSelected)
                {
                    @foreach (var hubId in _selectedHubIds)
                    {
                        var hub = Hubs.FirstOrDefault(h => h.Id == hubId);
                        if (hub != null)
                        {
                            <Badge Color="Color.Secondary" Class="me-2 mb-2"
                                   CloseClicked="@(async () => await OnHubSelectionChanged(hubId, false))">
                                @($"{hub.HubName}")
                            </Badge>
                        }
                    }
                }
                @if (_selectedOccupationIds.Count > 0 && !IsAllOccupationsSelected)
                {
                    @foreach (var occupationId in _selectedOccupationIds)
                    {
                        var occupation = TopOccupations.FirstOrDefault(o => o.Id == occupationId);
                        if (occupation != null)
                        {
                            <Badge Color="Color.Secondary" Class="me-2 mb-2"
                                   CloseClicked="@(async () => await OnOccupationSelectionChanged(occupationId, false))">
                                @($"{occupation.Name}")
                            </Badge>
                        }
                    }
                }
                @if (GetListInput.IsTemporary.HasValue)
                {
                    <Badge Color="Color.Secondary" Class="me-2 mb-2"
                           CloseClicked="@(async () => await OnStatusSelectionChanged(null))">
                        @(GetListInput.IsTemporary.Value ? L["Temporary"] : L["Registered"])
                    </Badge>
                }
                @if (GetListInput.Gender.HasValue)
                {
                    <Badge Color="Color.Secondary" Class="me-2 mb-2"
                           CloseClicked="@(async () => await OnGenderSelectionChanged(null))">
                        @(GetListInput.Gender.Value == Gender.Male ? L["Male"] : L["Female"])
                    </Badge>
                }
            </Column>
        </Row>

        <MemberDashboard Filter="@GetListInput" />
    </CardHeader>
    <CardBody>
        <DataGrid TItem="MemberDto"
                  Data="@Entities"
                  ReadData="@OnDataGridReadAsync"
                  TotalItems="@TotalCount"
                  ShowPager
                  ShowColumnChooser="true"
                  PageSize="@PageSize"
                  CurrentPage="@CurrentPage"
                  Responsive>
            <DataGridColumns>
                <DataGridColumn Field="@nameof(MemberDto.MboraId)" Caption="@L["MboraId"]">
                    <DisplayTemplate>
                        <div class="d-flex align-items-center">
                            <span>@context.MboraId</span>
                            <Button Color="Color.Light" Size="Size.Small" Margin="Margin.Is1.FromStart"
                                    @onclick="() => CopyToClipboard(context.MboraId.ToString())">
                                <Icon Name="IconName.Copy" Size="IconSize.Small" />
                            </Button>
                        </div>
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn Field="@nameof(MemberDto.UserFullName)" Caption="@L["FullName"]" />
                <DataGridColumn Field="@nameof(MemberDto.Gender)" Caption="@L["Gender"]">
                    <DisplayTemplate>
                        <Badge Color="@(context.Gender == Gender.Male ? Color.Success : Color.Primary)">
                            @(context.Gender == Gender.Male ? L["Male"] : L["Female"])
                        </Badge>
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn Field="@nameof(MemberDto.Temporary)" Caption="@L["Status"]" Sortable>
                    <DisplayTemplate>
                        <Badge Color="@(context.Temporary ? Color.Warning : Color.Success)">
                            @(context.Temporary ? L["Temporary"] : L["Permanent"])
                        </Badge>
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn Field="@nameof(MemberDto.MemberWallet)" Caption="@L["Wallet"]">
                    <DisplayTemplate>
                        MWK @(context.MemberWallet?.ToString("N2") ?? "0.00")
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn Field="@nameof(MemberDto.HubName)" Caption="@L["Hub"]" />
                <DataGridColumn Field="@nameof(MemberDto.VillageName)" Caption="@L["Village"]" />
                <DataGridColumn Field="@nameof(MemberDto.CurrentCycle)" Caption="@L["Group:CurrentCycle"]">
                    <DisplayTemplate>
                        @if (context.CurrentCycle != null)
                        {
                            <span class="d-flex gap-1"><Badge Color="Color.Primary" Class="fw-bold p-1 rounded-circle" Pill>@context.CurrentCycle.Value</Badge>@context.CurrentCycle.Label</span>
                        }
                        else
                        {
                            <span>-</span>
                        }
                        
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn Field="@nameof(MemberDto.Groups)" Caption="@L["Groups"]">
                    <DisplayTemplate>
                        <div class="d-flex flex-row flex-wrap gap-2">
                            @foreach (var group in context.Groups)
                            {
                                <Badge Color="Color.Secondary" Pill>@group.GroupName</Badge>
                            }
                        </div>
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn Field="@nameof(MemberDto.RegistrationDate)" Caption="@L["RegistrationDate"]">
                    <DisplayTemplate>
                        @(context.RegistrationDate.ToShortDateString())
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridEntityActionsColumn TItem="MemberDto" @ref="EntityActionsColumn" Width="150px">
                    <DisplayTemplate>
                        <EntityActions TItem="MemberDto" EntityActionsColumn="@EntityActionsColumn">
                            <EntityAction TItem="MemberDto"
                                          Text="@L["Edit"]"
                                          Visible="HasUpdatePermission"
                                          Clicked="() => NavigateToEditPage(context)" />

                            <EntityAction TItem="MemberDto"
                                          Text="@L["Delete"]"
                                          Visible="HasDeletePermission"
                                          Clicked="() => DeleteEntityAsync(context)"
                                          ConfirmationMessage="@(()=>L["DeleteConfirmationMessage"].Value)" />
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>
            </DataGridColumns>
            <EmptyTemplate>
                <EmptyState IconName="@EmptyStateIcon"
                            Title="@EmptyStateTitle"
                            Description="@EmptyStateDescription"
                            ShowCallToAction="@ShouldShowCallToAction"
                            CallToActionText="@CallToActionText"
                            OnCallToAction="NavigateToCreatePage" />
            </EmptyTemplate>
        </DataGrid>
    </CardBody>
</Card>

<!-- Create Member Modal with Stepper -->
<Modal @ref="CreateModal">
    <ModalContent Size="ModalSize.Fullscreen">
        <ModalHeader>
            <ModalTitle>@L["NewMember"]</ModalTitle>
            <CloseButton Clicked="CloseCreateModalAsync" />
        </ModalHeader>
        <ModalBody>
            <div style="display: contents;">
                <MemberFormStepper Member="@NewEntity"
                                   Hubs="@Hubs"
                                   Villages="@Villages"
                                   Occupations="@AllOccupations"
                                   OnSave="CreateEntityAsync"
                                   OnCancel="CloseCreateModalAsync" />
            </div>
        </ModalBody>
    </ModalContent>
</Modal>

<!-- Edit Member Modal with Stepper -->
<Modal @ref="EditModal">
    <ModalContent Size="ModalSize.Fullscreen">
        <ModalHeader>
            <ModalTitle>@L["EditMember"]</ModalTitle>
            <CloseButton Clicked="CloseEditModalAsync" />
        </ModalHeader>
        <ModalBody>
            <div style="display: contents;">
                <MemberFormStepper Member="@EditingEntity"
                                   Hubs="@Hubs"
                                   Villages="@Villages"
                                   Occupations="@AllOccupations"
                                   OnSave="UpdateEntityAsync"
                                   OnCancel="CloseEditModalAsync" />
            </div>
        </ModalBody>
    </ModalContent>
</Modal>



@code {
    // Clipboard functionality
    private async Task CopyToClipboard(string text)
    {
        await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
        await JSRuntime.InvokeVoidAsync("alert", L["CopiedToClipboard"].Value);
    }

    // Navigation methods
    private void NavigateToCreatePage()
    {
        NavigationManager.NavigateTo("members/new");
    }

    private void NavigateToEditPage(MemberDto member)
    {
        NavigationManager.NavigateTo($"members/{member.Id}/edit");
    }

    // Occupation filter methods
    private bool IsAllOccupationsSelected => _selectedOccupationIds.Count == TopOccupations.Count;
    private bool IsOccupationSelected(Guid occupationId) => _selectedOccupationIds.Contains(occupationId);

    private List<TabItem> Tabs { get; set; } = new();

    private async Task OnSelectAllOccupationsChanged(bool value)
    {
        _selectedOccupationIds.Clear();

        if (value)
        {
            // "All" is selected - add all occupations
            foreach (var occupation in TopOccupations)
            {
                _selectedOccupationIds.Add(occupation.Id);
            }

            // Disable filtering
            GetListInput.FilterByOccupations = false;
            GetListInput.OccupationIds = null;
        }
        else
        {
            // No occupations selected - enable filtering but with empty list
            GetListInput.FilterByOccupations = true;
            GetListInput.OccupationIds = new List<Guid>();
        }

        await SearchEntitiesAsync();

        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnOccupationSelectionChanged(Guid occupationId, bool selected)
    {
        if (selected)
        {
            _selectedOccupationIds.Add(occupationId);
        }
        else
        {
            _selectedOccupationIds.Remove(occupationId);
        }

        GetListInput.FilterByOccupations = _selectedOccupationIds.Count > 0;
        GetListInput.OccupationIds = _selectedOccupationIds.Count > 0 ? _selectedOccupationIds.ToList() : new List<Guid>();

        await SearchEntitiesAsync();

        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnStatusSelectionChanged(bool? isTemporary)
    {
        // Set the status filter value (null = All, true = Temporary, false = Registered)
        GetListInput.IsTemporary = isTemporary;

        // Refresh the data
        await SearchEntitiesAsync();

        // Close the filter dropdown if open
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnGenderSelectionChanged(Gender? gender)
    {
        // Set the gender filter value (null = All, Male, Female)
        GetListInput.Gender = gender;

        // Refresh the data
        await SearchEntitiesAsync();

        // Close the filter dropdown if open
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private DataGridHeaderEx dataGridHeader;
    private string currentFilterTab = "villages";
    private bool isFilterDropdownOpen = false;
    private List<OccupationLookupDto> TopOccupations = [];
    private List<OccupationLookupDto> AllOccupations = [];
    private readonly HashSet<Guid> _selectedOccupationIds;

    public Members() // Constructor
    {
        _selectedOccupationIds = new HashSet<Guid>();
        CreatePolicyName = MboraMaxNextPermissions.Members.Create;
        UpdatePolicyName = MboraMaxNextPermissions.Members.Edit;
        DeletePolicyName = MboraMaxNextPermissions.Members.Delete;
    }

    private async Task ExportToExcelAsync()
    {
        try
        {
            var excelBytes = await AppService.ExportToExcelAsync(GetListInput);
            if (excelBytes.Length == 0)
            {
                await Message.Error(L["NoDataToExport"]);
                return;
            }

            await JsRuntime.InvokeVoidAsync(
                "downloadFileFromStream",
                new
                {
                    ByteArray = excelBytes,
                    FileName = $"Members_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                    ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                });
        }
        catch (Exception ex)
        {
            await Message.Error(L["ExportError"]);
            await HandleErrorAsync(ex);
        }
    }

    // Override the base method to ensure proper initialization
    protected override async Task OpenCreateModalAsync()
    {
        // Ensure NewEntity is properly initialized with default values
        NewEntity = new CreateUpdateMemberDto
            {
                Gender = Gender.Male,
                MaritalStatus = MaritalStatus.ChooseOne,
                CriminalRecord = CriminalRecord.ChooseOne,
                LiteracyLevel = LiteracyLevel.ChooseOne
            };

        // Set default HubId and VillageId if available
        if (Hubs.Any())
        {
            NewEntity.HubId = Hubs.First().Id;
        }

        if (Villages.Any())
        {
            NewEntity.VillageId = Villages.First().Id;
        }

        await base.OpenCreateModalAsync();
    }

    private IEnumerable<VillageLookupDto> Villages { get; set; } = new List<VillageLookupDto>();
    private IEnumerable<HubLookupDto> Hubs { get; set; } = new List<HubLookupDto>();
    private readonly HashSet<Guid> _selectedVillageIds = [];
    private readonly HashSet<Guid> _selectedHubIds = [];

    // Filter visibility methods
    private Task OnFilterVisibilityChanged(bool isVisible)
    {
        isFilterDropdownOpen = isVisible;
        return Task.CompletedTask;
    }

    private Task OnFilterTabChanged(string tabName)
    {
        currentFilterTab = tabName;
        StateHasChanged();
        return Task.CompletedTask;
    }

    private string GetFilterTabDisplay(string tabName)
    {
        return currentFilterTab == tabName ? "block" : "none";
    }

    // Filter selection properties
    private bool IsAllVillagesSelected => _selectedVillageIds.Count == Villages.Count();
    private bool IsAllHubsSelected => _selectedHubIds.Count == Hubs.Count();

    // Empty state computed properties
    private IconName EmptyStateIcon =>
        (GetListInput.FilterByVillages && (GetListInput.VillageIds == null || GetListInput.VillageIds.Count == 0) ||
         GetListInput.FilterByHubs && (GetListInput.HubIds == null || GetListInput.HubIds.Count == 0) ||
         GetListInput.FilterByOccupations && (GetListInput.OccupationIds == null || GetListInput.OccupationIds.Count == 0) ||
         GetListInput.IsTemporary != null || GetListInput.Gender != null)
            ? IconName.Filter
            : !string.IsNullOrWhiteSpace(GetListInput.Filter) || HasDateFilter()
                ? IconName.Search
                : IconName.Users;

    private string EmptyStateTitle =>
        (GetListInput.FilterByVillages && (GetListInput.VillageIds == null || GetListInput.VillageIds.Count == 0) ||
         GetListInput.FilterByHubs && (GetListInput.HubIds == null || GetListInput.HubIds.Count == 0) ||
         GetListInput.FilterByOccupations && (GetListInput.OccupationIds == null || GetListInput.OccupationIds.Count == 0) ||
         GetListInput.IsTemporary != null || GetListInput.Gender != null)
            ? L["EmptyState:NoFiltersTitle"]
            : HasDateFilter()
                ? L["EmptyState:NoMembersInDateRangeTitle"]
                : !string.IsNullOrWhiteSpace(GetListInput.Filter)
                    ? L["EmptyState:NoMembersTitle"]
                    : L["EmptyState:NoMembersDefaultTitle"];

    private string EmptyStateDescription =>
        (GetListInput.FilterByVillages && (GetListInput.VillageIds == null || GetListInput.VillageIds.Count == 0) ||
         GetListInput.FilterByHubs && (GetListInput.HubIds == null || GetListInput.HubIds.Count == 0) ||
         GetListInput.FilterByOccupations && (GetListInput.OccupationIds == null || GetListInput.OccupationIds.Count == 0) ||
         GetListInput.IsTemporary != null || GetListInput.Gender != null)
            ? L["EmptyState:NoFiltersDescription"]
            : HasDateFilter()
                ? L["EmptyState:NoMembersInDateRangeDescription"]
                : !string.IsNullOrWhiteSpace(GetListInput.Filter)
                    ? L["EmptyState:NoMembersDescription"]
                    : L["EmptyState:NoMembersDefaultDescription"];

    private bool HasDateFilter() =>
        GetListInput.RegistrationStartDate.HasValue || GetListInput.RegistrationEndDate.HasValue;

    private bool ShouldShowCallToAction => HasCreatePermission &&
                                           !GetListInput.FilterByVillages &&
                                           !GetListInput.FilterByHubs &&
                                           !GetListInput.FilterByOccupations &&
                                           GetListInput.IsTemporary == null &&
                                           GetListInput.Gender == null &&
                                           !HasDateFilter() &&
                                           string.IsNullOrWhiteSpace(GetListInput.Filter);

    private string CallToActionText => L["EmptyState:AddMember"];

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        Tabs = [
            new TabItem("villages", L["Villages"]),
            new TabItem("hubs", L["Hubs"]),
            new TabItem("occupations", L["Occupations"]),
            new TabItem("status", L["Status"]),
            new TabItem("gender", L["Gender"])
       ];

        var villageResult = await AppService.GetVillageLookupAsync();
        Villages = villageResult.Items;
        var hubResult = await AppService.GetHubLookupAsync();
        Hubs = hubResult.Items;
        var occupationResult = await AppService.GetOccupationLookupAsync();
        TopOccupations = occupationResult.Items.Take(8).ToList();
        AllOccupations = occupationResult.Items.ToList();

        // Select all by default
        await OnSelectAllVillagesChanged(true);
        await OnSelectAllHubsChanged(true);
    }

    private bool IsVillageSelected(Guid villageId) =>
        IsAllVillagesSelected || _selectedVillageIds.Contains(villageId);

    private bool IsHubSelected(Guid hubId) =>
        IsAllHubsSelected || _selectedHubIds.Contains(hubId);

    private async Task OnSelectAllVillagesChanged(bool value)
    {
        _selectedVillageIds.Clear();
        if (value)
        {
            // "All" is selected - add all villages
            foreach (var village in Villages)
            {
                _selectedVillageIds.Add(village.Id);
            }

            // Disable filtering
            GetListInput.FilterByVillages = false;
            GetListInput.VillageIds = null;
        }
        else
        {
            // No villages selected - enable filtering but with empty list
            GetListInput.FilterByVillages = true;
            GetListInput.VillageIds = new List<Guid>();
        }

        await SearchEntitiesAsync();

        // Only close dropdown if a change was made to selection
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnSelectAllHubsChanged(bool value)
    {
        _selectedHubIds.Clear();
        if (value)
        {
            // "All" is selected - add all hubs
            foreach (var hub in Hubs)
            {
                _selectedHubIds.Add(hub.Id);
            }

            // Disable filtering
            GetListInput.FilterByHubs = false;
            GetListInput.HubIds = null;
        }
        else
        {
            // No hubs selected - enable filtering but with empty list
            GetListInput.FilterByHubs = true;
            GetListInput.HubIds = new List<Guid>();
        }

        await SearchEntitiesAsync();

        // Only close dropdown if a change was made to selection
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnVillageSelectionChanged(Guid villageId, bool selected)
    {
        if (selected)
        {
            _selectedVillageIds.Add(villageId);
        }
        else
        {
            _selectedVillageIds.Remove(villageId);
        }

        // Enable filtering by villages
        GetListInput.FilterByVillages = true;

        // Update filter based on selections
        GetListInput.VillageIds = _selectedVillageIds.Count > 0 ? _selectedVillageIds.ToList() : new List<Guid>();

        await SearchEntitiesAsync();

        // Only close dropdown if a change was made to selection
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnHubSelectionChanged(Guid hubId, bool selected)
    {
        if (selected)
        {
            _selectedHubIds.Add(hubId);
        }
        else
        {
            _selectedHubIds.Remove(hubId);
        }

        // Enable filtering by hubs
        GetListInput.FilterByHubs = true;

        // Update filter based on selections
        GetListInput.HubIds = _selectedHubIds.Count > 0 ? _selectedHubIds.ToList() : new List<Guid>();

        await SearchEntitiesAsync();

        // Only close dropdown if a change was made to selection
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    protected override async Task SearchEntitiesAsync()
    {
        CurrentPage = 1;
        await GetEntitiesAsync();
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnDateRangeChanged((DateTime? Start, DateTime? End) range)
    {
        // Set date range values
        GetListInput.RegistrationStartDate = range.Start;

        // If end date is set, extend it to end of day
        GetListInput.RegistrationEndDate = range.End.HasValue ? range.End.Value.Date.AddDays(1).AddTicks(-1) : range.End;

        // Execute search with the date filter
        await SearchEntitiesAsync();
    }

    protected virtual async Task OnSearchChanged(string? value)
    {
        GetListInput.Filter = value;
        await SearchEntitiesAsync();
    }

}