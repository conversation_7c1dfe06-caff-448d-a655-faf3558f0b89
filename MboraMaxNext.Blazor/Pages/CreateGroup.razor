@page "/groups/new"
@attribute [Authorize(MboraMaxNextPermissions.Groups.Create)]
@inherits MboraMaxNextComponentBase
@using MboraMaxNext.Localization
@using MboraMaxNext.Permissions
@using MboraMaxNext.Services.Administration
@using MboraMaxNext.Services.Dtos.Administration.Groups
@using MboraMaxNext.Services.Dtos.Administration.Hubs
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using MboraMaxNext.Components.Groups
@using MboraMaxNext.Services.Dtos.Administration.Services

@inject IStringLocalizer<MboraMaxNextResource> L
@inject IJSRuntime JsRuntime
@inject IGroupAppService AppService
@inject NavigationManager NavigationManager

<Card>
    <CardHeader>
        <HeadContent>
            <h2>@L["NewGroup"]</h2>
        </HeadContent>
    </CardHeader>
    <CardBody>
        <GroupFormStepper
            Group="@NewGroup"
            Hubs="Hubs"
            Users="Users"
            Services="Services"
            OnSave="CreateGroupAsync"
            OnCancel="NavigateToGroups"/>
    </CardBody>
</Card>


@code {

    private CreateUpdateGroupDto NewGroup { get; set; } = new();

    [Parameter] public IEnumerable<HubLookupDto> Hubs { get; set; } = new List<HubLookupDto>();
    [Parameter] public IEnumerable<UserLookupDto> Users { get; set; } = new List<UserLookupDto>();
    [Parameter] public IEnumerable<ServiceLookupDto> Services { get; set; } = new List<ServiceLookupDto>();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        Hubs = (await AppService.GetHubLookupAsync()).Items;
        Users = (await AppService.GetUsersLookupAsync()).Items;
        Services = (await AppService.GetServiceLookupAsync()).Items;
    }

    private async Task CreateGroupAsync(CreateUpdateGroupDto group)
    {
        try
        {
            await AppService.CreateAsync(group);
            NavigateToGroups();
        }
        catch (Exception ex)
        {
            await HandleErrorAsync(ex);
        }
    }

    private void NavigateToGroups()
    {
        NavigationManager.NavigateTo("groups");
    }

}
