@page "/groups"
@namespace MboraMaxNext.Blazor.Pages.GroupsList

@attribute [Authorize(MboraMaxNextPermissions.Groups.Default)]
@using MboraMaxNext.Components.Groups
@using MboraMaxNext.Localization
@using MboraMaxNext.Permissions
@using MboraMaxNext.Services.Dtos.Administration.Hubs
@using MboraMaxNext.Services.Dtos.Administration.Groups
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Volo.Abp.AspNetCore.Components.Web
@using MboraMaxNext.Models
@using MboraMaxNext.Services.Administration
@using MboraMaxNext.Services.Dtos.Administration.Services
@inject IStringLocalizer<MboraMaxNextResource> L
@inject AbpBlazorMessageLocalizerHelper<MboraMaxNextResource> LH
@inject NavigationManager NavigationManager
@inject IJSRuntime JsRuntime
@inject IGroupAppService AppService

@inherits AbpCrudPageBase<MboraMaxNext.Services.Administration.IGroupAppService, GroupDto, Guid, GetGroupsInput, CreateUpdateGroupDto>

<PageTitle>@L["Groups"]</PageTitle>

<Card>
    <CardHeader>
        <DataGridHeaderEx @ref="dataGridHeader"
                          Title="@L["Groups"]"
                          HasCreatePermission="@HasCreatePermission"
                          HasExportPermission="@HasCreatePermission"
                          ShowDateRange="true"
                          DateRangeLabel="@L["RegistrationDateRange"]"
                          StartDate="@GetListInput.CreationTimeStartDate"
                          EndDate="@GetListInput.CreationTimeEndDate"
                          SearchText="@GetListInput.Filter"
                          Tabs="@Tabs"
                          DefaultTab="services"
                          OnDateRangeChanged="@OnDateRangeChanged"
                          OnSearchChanged="@OnSearchChanged"
                          OnCreateClicked="@NavigateToCreatePage"
                          OnExportClicked="@ExportToExcelAsync"
                          OnFilterVisibilityChanged="@OnFilterVisibilityChanged"
                          OnFilterTabChanged="@OnFilterTabChanged">
            <Filters>
                <div class="services-filter" style="display: @GetFilterTabDisplay("services")">
                    <Field>
                        <Check TValue="bool"
                               Checked="@IsAllServicesSelected"
                               CheckedChanged="@(async (value) => await OnSelectAllServicesChanged(value))">
                            @L["All"]
                        </Check>
                    </Field>
                    <div style="max-height: 250px; overflow-y: auto;">
                        @foreach (var service in Services)
                        {
                            <Field>
                                <Check TValue="bool"
                                       Checked="@IsServiceSelected(service.Id)"
                                       CheckedChanged="@(async (value) => await OnServiceSelectionChanged(service.Id, value))">
                                    @service.Description
                                </Check>
                            </Field>
                        }
                    </div>
                </div>

                <div class="hubs-filter" style="display: @GetFilterTabDisplay("hubs")">
                    <Field>
                        <Check TValue="bool"
                               Checked="@IsAllHubsSelected"
                               CheckedChanged="@(async (value) => await OnSelectAllHubsChanged(value))">
                            @L["All"]
                        </Check>
                    </Field>
                    <div style="max-height: 250px; overflow-y: auto;">
                        @foreach (var hub in Hubs)
                        {
                            <Field>
                                <Check TValue="bool"
                                       Checked="@IsHubSelected(hub.Id)"
                                       CheckedChanged="@(async (value) => await OnHubSelectionChanged(hub.Id, value))">
                                    @hub.HubName
                                </Check>
                            </Field>
                        }
                    </div>
                </div>
            </Filters>
        </DataGridHeaderEx>

        <Row Class="mt-2">
            <Column>
                @if (_selectedServicesIds.Count > 0 && !IsAllServicesSelected)
                {
                    @foreach (var serviceId in _selectedServicesIds)
                    {
                        var service = Services.FirstOrDefault(v => v.Id == serviceId);
                        if (service != null)
                        {
                            <Badge Color="Color.Secondary" Class="me-2 mb-2"
                                   CloseClicked="@(async () => await OnServiceSelectionChanged(serviceId, false))">
                                @($"{service.Description}")
                            </Badge>
                        }
                    }
                }
                @if (_selectedHubIds.Count > 0 && !IsAllHubsSelected)
                {
                    @foreach (var hubId in _selectedHubIds)
                    {
                        var hub = Hubs.FirstOrDefault(h => h.Id == hubId);
                        if (hub != null)
                        {
                            <Badge Color="Color.Secondary" Class="me-2 mb-2"
                                   CloseClicked="@(async () => await OnHubSelectionChanged(hubId, false))">
                                @($"{hub.HubName}")
                            </Badge>
                        }
                    }
                }
            </Column>
        </Row>

        <GroupsDashboard Filter="@GetListInput"/>
    </CardHeader>

    <CardBody>
        <DataGrid TItem="GroupDto"
                  Data="@Entities"
                  ReadData="@OnDataGridReadAsync"
                  TotalItems="@TotalCount"
                  ShowPager
                  ShowColumnChooser="true"
                  PageSize="@PageSize"
                  CurrentPage="@CurrentPage"
                  Responsive>
            <DataGridColumns>
                <DataGridColumn Field="@nameof(GroupDto.GroupName)" Caption="@L["Group:Name"]">
                    <DisplayTemplate>
                        <strong>@context.GroupName</strong>
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn TItem="GroupDto" Field="Hub.HubName" Caption="@L["Hub"]"/>
                <DataGridColumn TItem="GroupDto" Field="Chair" Caption="@L["Group:Chair"]"/>
                <DataGridColumn TItem="GroupDto" Field="Secretary" Caption="@L["Group:Secretary"]"/>
                <DataGridColumn Field="@nameof(GroupDto.StartDate)" Caption="@L["Group:StartDate"]">
                    <DisplayTemplate>
                        @(context.StartDate?.ToShortDateString())
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn Field="@nameof(GroupDto.CycleNumber)" Caption="@L["Group:CycleNumber"]"/>
                <DataGridColumn TItem="GroupDto" Field="@nameof(GroupDto.MemberCount)" Caption="@L["Group:Size"]">
                    <DisplayTemplate>
                        <div class="d-flex align-items-center">
                            <span class="me-2">@context.MemberCount</span>
                            <Progress Value="@context.MemberCount" Max="@context.MemberCountComparedToMax"
                                      Color="Color.Info"
                                      Size="Size.Small" Style="width: 100px;"/>
                        </div>
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn TItem="GroupDto" Caption="@L["Group:Financial"]">
                    <DisplayTemplate>
                        <div>
                            <Progress Size="Size.Small" ShowValue="false">
                                @foreach (var progress in GetGroupPercentages(context))
                                {
                                    <ProgressBar Value="@progress.value"
                                                 Color="@progress.color">
                                        <Tooltip Text="@progress.label"
                                                 Placement="TooltipPlacement.Top">
                                            <div class="w-100" style="height: 6px;"></div>
                                        </Tooltip>
                                    </ProgressBar>
                                }
                            </Progress>
                        </div>
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridEntityActionsColumn TItem="GroupDto" @ref="EntityActionsColumn" Width="150px">
                    <DisplayTemplate>
                        <EntityActions TItem="GroupDto" EntityActionsColumn="@EntityActionsColumn">
                            <EntityAction TItem="GroupDto"
                                          Text="@L["Edit"]"
                                          Visible="HasUpdatePermission"
                                          Clicked="() => NavigateToEditPage(context)"/>

                            <EntityAction TItem="GroupDto"
                                          Text="@L["Group:ManageMembers"]"
                                          Visible="HasUpdatePermission"
                                          Clicked="() => NavigateToMemberManagement(context)"/>

                            <EntityAction TItem="GroupDto"
                                          Text="@L["Delete"]"
                                          Visible="HasDeletePermission"
                                          Clicked="() => DeleteEntityAsync(context)"
                                          ConfirmationMessage="@(() => L["DeleteConfirmationMessage"].Value)"/>
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>
            </DataGridColumns>
            <EmptyTemplate>
                <EmptyState IconName="@EmptyStateIcon"
                            Title="@EmptyStateTitle"
                            Description="@EmptyStateDescription"
                            ShowCallToAction="@ShouldShowCallToAction"
                            CallToActionText="@CallToActionText"
                            OnCallToAction="NavigateToCreatePage"/>
            </EmptyTemplate>
        </DataGrid>
    </CardBody>
</Card>


@code {
    private IEnumerable<HubLookupDto> Hubs { get; set; } = new List<HubLookupDto>();
    private IEnumerable<ServiceLookupDto> Services { get; set; } = new List<ServiceLookupDto>();
    private readonly HashSet<Guid> _selectedServicesIds = [];
    private readonly HashSet<Guid> _selectedHubIds = [];

    private DataGridHeaderEx dataGridHeader;
    private string currentFilterTab = "services";
    private bool isFilterDropdownOpen = false;

    private bool IsAllServicesSelected => _selectedServicesIds.Count == Services.Count();
    private bool IsAllHubsSelected => _selectedHubIds.Count == Hubs.Count();

    private List<TabItem> Tabs { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        Tabs =
        [
            new TabItem("services", L["Services"]),
            new TabItem("hubs", L["Hubs"]),
        ];

        var servicesResult = await AppService.GetServiceLookupAsync();
        Services = servicesResult.Items;

        var hubResult = await AppService.GetHubLookupAsync();
        Hubs = hubResult.Items;

        // Select all by default
        await OnSelectAllHubsChanged(true);
        await OnSelectAllServicesChanged(true);
    }

    // Navigation methods
    private void NavigateToCreatePage()
    {
        NavigationManager.NavigateTo("groups/new");
    }

    private bool IsHubSelected(Guid hubId) =>
        IsAllHubsSelected || _selectedHubIds.Contains(hubId);

    private bool IsServiceSelected(Guid serviceId) =>
        IsAllServicesSelected || _selectedServicesIds.Contains(serviceId);

    // Empty state computed properties
    private IconName EmptyStateIcon =>
        (GetListInput.FilterByServices && (GetListInput.ServiceIds == null || GetListInput.ServiceIds.Count == 0) ||
         GetListInput.FilterByHubs && (GetListInput.HubIds == null || GetListInput.HubIds.Count == 0))
            ? IconName.Filter
            : !string.IsNullOrWhiteSpace(GetListInput.Filter) || HasDateFilter()
                ? IconName.Search
                : IconName.Users;

    private string EmptyStateTitle =>
        (GetListInput.FilterByServices && (GetListInput.ServiceIds == null || GetListInput.ServiceIds.Count == 0) ||
         GetListInput.FilterByHubs && (GetListInput.HubIds == null || GetListInput.HubIds.Count == 0))
            ? L["EmptyState:NoFiltersTitle"]
            : HasDateFilter()
                ? L["EmptyState:NoMembersInDateRangeTitle"]
                : !string.IsNullOrWhiteSpace(GetListInput.Filter)
                    ? L["EmptyState:NoMembersTitle"]
                    : L["EmptyState:NoMembersDefaultTitle"];

    private string EmptyStateDescription =>
        (GetListInput.FilterByServices && (GetListInput.ServiceIds == null || GetListInput.ServiceIds.Count == 0) ||
         GetListInput.FilterByHubs && (GetListInput.HubIds == null || GetListInput.HubIds.Count == 0))
            ? L["EmptyState:NoFiltersDescription"]
            : HasDateFilter()
                ? L["EmptyState:NoMembersInDateRangeDescription"]
                : !string.IsNullOrWhiteSpace(GetListInput.Filter)
                    ? L["EmptyState:NoMembersDescription"]
                    : L["EmptyState:NoMembersDefaultDescription"];

    private bool HasDateFilter() => GetListInput.CreationTimeStartDate.HasValue || GetListInput.CreationTimeEndDate.HasValue;

    private bool ShouldShowCallToAction => HasCreatePermission &&
                                           !GetListInput.FilterByServices &&
                                           !GetListInput.FilterByHubs &&
                                           !HasDateFilter() &&
                                           string.IsNullOrWhiteSpace(GetListInput.Filter);

    private string CallToActionText => L["EmptyState:AddMember"];

    public Groups()
    {
        CreatePolicyName = MboraMaxNextPermissions.Groups.Create;
        UpdatePolicyName = MboraMaxNextPermissions.Groups.Edit;
        DeletePolicyName = MboraMaxNextPermissions.Groups.Delete;
    }

    private Task OnFilterVisibilityChanged(bool isVisible)
    {
        isFilterDropdownOpen = isVisible;
        return Task.CompletedTask;
    }

    private Task OnFilterTabChanged(string tabName)
    {
        currentFilterTab = tabName;
        StateHasChanged();
        return Task.CompletedTask;
    }

    private async Task OnSelectAllHubsChanged(bool value)
    {
        _selectedHubIds.Clear();
        if (value)
        {
            // "All" is selected - add all hubs
            foreach (var hub in Hubs)
            {
                _selectedHubIds.Add(hub.Id);
            }

            // Disable filtering
            GetListInput.FilterByHubs = false;
            GetListInput.HubIds = null;
        }
        else
        {
            // No hubs selected - enable filtering but with empty list
            GetListInput.FilterByHubs = true;
            GetListInput.HubIds = new List<Guid>();
        }

        await SearchEntitiesAsync();

        // Only close dropdown if a change was made to selection
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnHubSelectionChanged(Guid hubId, bool selected)
    {
        if (selected)
        {
            _selectedHubIds.Add(hubId);
        }
        else
        {
            _selectedHubIds.Remove(hubId);
        }

        // Enable filtering by hubs
        GetListInput.FilterByHubs = true;

        // Update filter based on selections
        GetListInput.HubIds = _selectedHubIds.Count > 0 ? _selectedHubIds.ToList() : new List<Guid>();

        await SearchEntitiesAsync();

        // Only close dropdown if a change was made to selection
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnSelectAllServicesChanged(bool value)
    {
        _selectedServicesIds.Clear();
        if (value)
        {
            // "All" is selected - add all Services
            foreach (var service in Services)
            {
                _selectedServicesIds.Add(service.Id);
            }

            // Disable filtering
            GetListInput.FilterByServices = false;
            GetListInput.ServiceIds = null;
        }
        else
        {
            // No Services selected - enable filtering but with empty list
            GetListInput.FilterByServices = true;
            GetListInput.ServiceIds = new List<Guid>();
        }

        await SearchEntitiesAsync();

        // Only close dropdown if a change was made to selection
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    private async Task OnServiceSelectionChanged(Guid serviceId, bool selected)
    {
        if (selected)
        {
            _selectedServicesIds.Add(serviceId);
        }
        else
        {
            _selectedServicesIds.Remove(serviceId);
        }

        GetListInput.FilterByServices = true;

        GetListInput.ServiceIds = _selectedServicesIds.Count > 0 ? _selectedServicesIds.ToList() : new List<Guid>();

        await SearchEntitiesAsync();

        // Only close dropdown if a change was made to selection
        if (dataGridHeader != null && isFilterDropdownOpen)
        {
            await dataGridHeader.HideFilterDropdown();
        }
    }

    protected virtual async Task OnSearchChanged(string? value)
    {
        GetListInput.Filter = value;
        await SearchEntitiesAsync();
    }

    private async Task ExportToExcelAsync()
    {
        try
        {
            var excelBytes = await AppService.ExportToExcelAsync(GetListInput);
            if (excelBytes.Length == 0)
            {
                await Message.Error(L["NoDataToExport"]);
                return;
            }

            await JsRuntime.InvokeVoidAsync(
                "downloadFileFromStream",
                new
                {
                    ByteArray = excelBytes,
                    FileName = $"Members_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                    ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                });
        }
        catch (Exception ex)
        {
            await Message.Error(L["ExportError"]);
            await HandleErrorAsync(ex);
        }
    }

    private async Task OnDateRangeChanged((DateTime? Start, DateTime? End) range)
    {
        // Set date range values
        GetListInput.CreationTimeStartDate = range.Start;

        // If end date is set, extend it to end of day
        GetListInput.CreationTimeEndDate = range.End.HasValue ? range.End.Value.Date.AddDays(1).AddTicks(-1) : range.End;

        // Execute search with the date filter
        await SearchEntitiesAsync();
    }

    private string GetFilterTabDisplay(string tabName)
    {
        return currentFilterTab == tabName ? "block" : "none";
    }

    private void NavigateToEditPage(GroupDto group)
    {
        NavigationManager.NavigateTo($"groups/{group.Id}/edit");
    }

    private void NavigateToMemberManagement(GroupDto group)
    {
        NavigationManager.NavigateTo($"groups/{group.Id}/members");
    }

    private IEnumerable<(string label, int value, Color color)> GetGroupPercentages(GroupDto context)
    {
        var groupPercentage = context.GroupPercentage ?? 0;
        var mboraPercentage = context.MboraPercentage ?? 0;
        return new List<(string label, int value, Color color)>
        {
            ($"{L["Group:Percentage"]} {context.GroupPercentage:0.##}%", (int)groupPercentage, Color.Success),
            ($"{L["Group:MboraPercentage"]} {context.MboraPercentage:0.##}%", (int)mboraPercentage, color: Color.Warning)
        }.OrderBy(x => x.value);
    }

}