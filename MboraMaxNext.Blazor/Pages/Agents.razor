@page "/agents"
@attribute [Authorize(MboraMaxNextPermissions.Agents.Default)]
@using MboraMaxNext.Entities.Administration
@using MboraMaxNext.Localization
@using MboraMaxNext.Permissions
@using MboraMaxNext.Services.Dtos.Administration.Agents
@using MboraMaxNext.Services.Dtos.Administration.Hubs
@using Volo.Abp.Application.Dtos
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Domain.Entities
@using MboraMaxNext.Components
@using Microsoft.JSInterop
@inject IStringLocalizer<MboraMaxNextResource> L
@inject AbpBlazorMessageLocalizerHelper<MboraMaxNextResource> LH
@inject IJSRuntime JsRuntime
@inherits AbpCrudPageBase<MboraMaxNext.Services.Administration.IAgentAppService, AgentDto, Guid, MboraMaxNext.Services.Administration.GetAgentsInput, CreateUpdateAgentDto>

<PageTitle>@L["Agents"]</PageTitle>

@code {
    private async Task ExportToExcelAsync()
    {
        try
        {
            var excelBytes = await AppService.ExportToExcelAsync(GetListInput);
            if (excelBytes.Length == 0)
            {
                await Message.Error(L["NoDataToExport"]);
                return;
            }

            await JsRuntime.InvokeVoidAsync(
                "downloadFileFromStream",
                new
                {
                    ByteArray = excelBytes,
                    FileName = $"Agents_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                    ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                });
        }
        catch (Exception ex)
        {
            await Message.Error(L["ExportError"]);
            await HandleErrorAsync(ex);
        }
    }

}

<Card>
    <CardHeader>
        <DataGridHeader TItem="HubLookupDto"
                        Title="@L["Agents"]"
                        Label="@L["FilterByHub"]"
                        HasCreatePermission="@HasCreatePermission"
                        FilterItems="@Hubs"
                        IsAllSelected="@IsAllHubsSelected"
                        IsItemSelected="@(hub => IsHubSelected(hub.Id))"
                        SearchText="@GetListInput.Filter"
                        OnAllSelectedChanged="@OnSelectAllHubsChanged"
                        OnItemSelectionChanged="@(async (tuple) => await OnHubSelectionChanged(tuple.id.Id, tuple.selected))"
                        OnSearchChanged="@OnSearchTextChanged"
                        OnCreateClicked="@OpenCreateModalAsync"
                        OnExportClicked="@ExportToExcelAsync">
            <ItemTemplate Context="hub">
                @($"{hub.HubCode} - {hub.HubName}")
            </ItemTemplate>
        </DataGridHeader>
        @if (_selectedHubIds.Count > 0 && !IsAllHubsSelected)
        {
            <Row Class="mt-2">
                <Column>
                    @foreach (var hubId in _selectedHubIds)
                    {
                        var hub = Hubs.FirstOrDefault(h => h.Id == hubId);
                        if (hub != null)
                        {
                            <Badge Color="Color.Secondary" Class="me-2 mb-2"
                                   CloseClicked="@(async () => await OnHubSelectionChanged(hubId, false))">
                                @($"{hub.HubCode} - {hub.HubName}")
                            </Badge>
                        }
                    }
                </Column>
            </Row>
        }
    </CardHeader>
    <CardBody>
        <DataGrid TItem="AgentDto"
                  Responsive="true"
                  ShowColumnChooser="true"
                  Data="Entities"
                  ReadData="OnDataGridReadAsync"
                  TotalItems="TotalCount"
                  ShowPager="true"
                  PageSize="PageSize"
        >
            <DataGridColumns>
                <DataGridColumn TItem="AgentDto"
                                Field="@nameof(AgentDto.AgentCode)"
                                Caption="@L["AgentCode"]">
                    <DisplayTemplate>
                        <div class="d-flex align-items-center">
                            <span>@context.AgentCode</span>
                            <Button Color="Color.Light" Size="Size.Small" Margin="Margin.Is1.FromStart"
                                    @onclick="() => CopyToClipboard(context.AgentCode)">
                                <Icon Name="IconName.Copy" Size="IconSize.Small"/>
                            </Button>
                        </div>
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="AgentDto"
                                Field="@nameof(AgentDto.UserFullName)"
                                Caption="@L["Name"]"/>

                <DataGridColumn TItem="AgentDto"
                                Field="@nameof(AgentDto.AgentType)"
                                Caption="@L["AgentType"]"/>

                <DataGridColumn TItem="AgentDto"
                                Field="@nameof(AgentDto.AgentCommissionRate)"
                                Caption="@L["CommissionRate"]">
                    <DisplayTemplate>
                        <Badge Color="Color.Info" TextColor="TextColor.White">
                            @(context.AgentCommissionRate)%
                        </Badge>
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="AgentDto"
                                Field="@nameof(AgentDto.MaximumTransactionAmount)"
                                Caption="@L["MaxTransAmount"]">
                    <DisplayTemplate>
                        MWK @(context.MaximumTransactionAmount?.ToString("N2") ?? "0.00")
                    </DisplayTemplate>
                </DataGridColumn>


                <DataGridEntityActionsColumn TItem="AgentDto" @ref="@EntityActionsColumn">
                    <DisplayTemplate>
                        <EntityActions TItem="AgentDto" EntityActionsColumn="@EntityActionsColumn">
                            <EntityAction TItem="AgentDto"
                                          Text="@L["Edit"]"
                                          Visible="HasUpdatePermission"
                                          Clicked="() => OpenEditModalAsync(context)"/>

                            <EntityAction TItem="AgentDto"
                                          Text="@L["Delete"]"
                                          Visible="HasDeletePermission"
                                          Clicked="() => DeleteEntityAsync(context)"
                                          ConfirmationMessage="@(() => L["DeleteConfirmationMessage"].Value)"/>
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>
            </DataGridColumns>
            <EmptyTemplate>
                <EmptyState
                    IconName="@EmptyStateIcon"
                    Title="@EmptyStateTitle"
                    Description="@EmptyStateDescription"
                    ShowCallToAction="@ShouldShowCallToAction"
                    CallToActionText="@CallToActionText"
                    OnCallToAction="OpenCreateModalAsync"/>
            </EmptyTemplate>
        </DataGrid>
    </CardBody>
</Card>

<Modal @ref="@CreateModal">
    <ModalContent IsCentered="true">
        <Form>
            <ModalHeader>
                <ModalTitle>@L["NewAgent"]</ModalTitle>
                <CloseButton Clicked="CloseCreateModalAsync"/>
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@CreateValidationsRef" Model="@NewEntity" ValidateOnLoad="false">
                    <Field>
                        <FieldLabel>@L["User"]</FieldLabel>
                        <Autocomplete TItem="UserLookupDto"
                                      TValue="Guid"
                                      Data="@AgentEntities"
                                      TextField="@(item => item.Name)"
                                      ValueField="@(item => item.Id)"
                                      @bind-SelectedValue="@NewEntity.AgentUserId"
                                      Placeholder="@L["ChooseAgent"]"
                                      FreeTyping
                                      MinLength="0">
                        </Autocomplete>
                    </Field>

                    <Field>
                        <FieldLabel>@L["AgentType"]</FieldLabel>
                        <Select TValue="AgentType" @bind-SelectedValue="@NewEntity.AgentType">
                            @foreach (var type in Enum.GetValues<AgentType>())
                            {
                                <SelectItem Value="@type">@L[$"Enum:AgentType:{(int)type}"]</SelectItem>
                            }
                        </Select>
                    </Field>

                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["CommissionRate"]</FieldLabel>
                            <NumericEdit TValue="int?" @bind-Value="@NewEntity.AgentCommissionRate"/>
                        </Field>
                    </Validation>

                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["MaxTransAmount"]</FieldLabel>
                            <NumericEdit TValue="decimal?" @bind-Value="@NewEntity.MaximumTransactionAmount"/>
                        </Field>
                    </Validation>

                    <Field>
                        <FieldLabel>@L["Hubs"]</FieldLabel>
                        <Autocomplete TItem="HubLookupDto"
                                      TValue="Guid"
                                      Data="@Hubs"
                                      TextField="@(hub => $"{hub.HubCode} - {hub.HubName}")"
                                      ValueField="@(hub => hub.Id)"
                                      @bind-SelectedValues="@NewEntity.HubIds"
                                      SelectionMode="AutocompleteSelectionMode.Multiple"
                                      Placeholder="Search hubs..."
                                      FreeTyping
                                      Filter="AutocompleteFilter.Contains"
                                      Class="bg-white"
                                      SelectedItemsClass="bg-white"
                                      DropdownClass="bg-white">
                            <NotFoundContent>No matching hubs found</NotFoundContent>
                        </Autocomplete>
                    </Field>
                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseCreateModalAsync">
                    @L["Cancel"]
                </Button>
                <Button Color="Color.Primary"
                        Type="@ButtonType.Submit"
                        PreventDefaultOnSubmit="true"
                        Clicked="CreateEntityAsync">
                    @L["Save"]
                </Button>
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>

<Modal @ref="@EditModal">
    <ModalContent IsCentered="true">
        <Form>
            <ModalHeader>
                <ModalTitle>@L["EditAgent"]</ModalTitle>
                <CloseButton Clicked="CloseEditModalAsync"/>
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@EditValidationsRef" Model="@EditingEntity" ValidateOnLoad="false">
                    <Field>
                        <FieldLabel>@L["User"]</FieldLabel>
                        <Autocomplete TItem="UserLookupDto"
                                      TValue="Guid"
                                      Data="@AgentEntities"
                                      Search="@SearchedUser"
                                      @bind-SelectedValue="@EditingEntity.AgentUserId"
                                      TextField="@(item => item.Name)"
                                      ValueField="@(item => item.Id)"
                                      Placeholder="@L["SelectUser"]"
                                      Disabled="true"/>
                    </Field>

                    <Field>
                        <FieldLabel>@L["AgentType"]</FieldLabel>
                        <Select TValue="AgentType" @bind-SelectedValue="@EditingEntity.AgentType">
                            @foreach (var type in Enum.GetValues<AgentType>())
                            {
                                <SelectItem Value="@type">@L[$"Enum:AgentType:{(int)type}"]</SelectItem>
                            }
                        </Select>
                    </Field>

                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["CommissionRate"]</FieldLabel>
                            <NumericEdit TValue="int?" @bind-Value="@EditingEntity.AgentCommissionRate"/>
                        </Field>
                    </Validation>

                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["MaxTransAmount"]</FieldLabel>
                            <NumericEdit TValue="decimal?" @bind-Value="@EditingEntity.MaximumTransactionAmount"/>
                        </Field>
                    </Validation>

                    <Field>
                        <FieldLabel>@L["Hubs"]</FieldLabel>
                        <Autocomplete TItem="HubLookupDto"
                                      TValue="Guid"
                                      Data="@Hubs"
                                      TextField="@(hub => $"{hub.HubCode} - {hub.HubName}")"
                                      ValueField="@(hub => hub.Id)"
                                      @bind-SelectedValues="EditingEntity.HubIds"
                                      SelectionMode="AutocompleteSelectionMode.Multiple"
                                      Placeholder="Search hubs..."
                                      MultipleBadgeColor="Color.Secondary"
                                      SuggestSelectedItems="true"
                                      Filter="AutocompleteFilter.Contains"
                                      Class="bg-white"
                                      SelectedItemsClass="bg-white"
                                      DropdownClass="bg-white">
                            <NotFoundContent>No matching hubs found</NotFoundContent>
                        </Autocomplete>
                    </Field>

                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseEditModalAsync">
                    @L["Cancel"]
                </Button>
                <Button Color="Color.Primary"
                        Type="@ButtonType.Submit"
                        PreventDefaultOnSubmit="true"
                        Clicked="UpdateEntityAsync">
                    @L["Save"]
                </Button>
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>

@code {

    // Clipboard functionality
    private async Task CopyToClipboard(string text)
    {
        await JsRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
        await JsRuntime.InvokeVoidAsync("alert", L["CopiedToClipboard"].Value);
    }

    private IReadOnlyList<UserLookupDto> AgentEntities { get; set; } = [];
    private IEnumerable<HubLookupDto> Hubs { get; set; } = [];
    private string SearchedUser { get; set; } = string.Empty;
    private readonly HashSet<Guid> _selectedHubIds = [];

    // Empty state computed properties
    private IconName EmptyStateIcon => GetListInput.FilterByHubs && (GetListInput.HubIds == null || !GetListInput.HubIds.Any())
        ? IconName.Filter
        : !string.IsNullOrWhiteSpace(GetListInput.Filter)
            ? IconName.Search
            : IconName.Users;

    private string EmptyStateTitle => GetListInput.FilterByHubs && (GetListInput.HubIds == null || !GetListInput.HubIds.Any())
        ? L["EmptyState:NoHubsTitle"]
        : !string.IsNullOrWhiteSpace(GetListInput.Filter)
            ? L["EmptyState:NoAgentsTitle"]
            : L["EmptyState:NoAgentsDefaultTitle"];

    private string EmptyStateDescription => GetListInput.FilterByHubs && (GetListInput.HubIds == null || !GetListInput.HubIds.Any())
        ? L["EmptyState:NoHubsDescription"]
        : !string.IsNullOrWhiteSpace(GetListInput.Filter)
            ? L["EmptyState:NoAgentsDescription"]
            : L["EmptyState:NoAgentsDefaultDescription"];

    private bool ShouldShowCallToAction => HasCreatePermission &&
                                           !GetListInput.FilterByHubs &&
                                           string.IsNullOrWhiteSpace(GetListInput.Filter);

    private string CallToActionText => L["EmptyState:AddAgent"];

    private bool IsAllHubsSelected => _selectedHubIds.Count == Hubs.Count();

    public Agents() // Constructor
    {
        CreatePolicyName = MboraMaxNextPermissions.Agents.Create;
        UpdatePolicyName = MboraMaxNextPermissions.Agents.Edit;
        DeletePolicyName = MboraMaxNextPermissions.Agents.Delete;
    }

    private bool IsHubSelected(Guid hubId)
    {
        return IsAllHubsSelected || _selectedHubIds.Contains(hubId);
    }

    private async Task OnSelectAllHubsChanged(bool value)
    {
        _selectedHubIds.Clear();
        if (value)
        {
            // "All" is selected:
            // - Populate _selectedHubIds with all available hub IDs.
            foreach (var hub in Hubs)
            {
                _selectedHubIds.Add(hub.Id);
            }

            // Disable hub filtering so that the service returns all agents.
            GetListInput.FilterByHubs = false;
            GetListInput.HubIds = null;
        }
        else
        {
            // "All" is deselected:
            // - Clear the selected hubs.
            // Enable hub filtering.
            GetListInput.FilterByHubs = true;
            // With no hubs selected, the backend will return no agents.
            GetListInput.HubIds = [];
        }

        await SearchEntitiesAsync();
    }


    private async Task OnHubSelectionChanged(Guid hubId, bool value)
    {
        // Enable hub filtering when an individual hub selection is changed.
        GetListInput.FilterByHubs = true;

        if (value)
        {
            _selectedHubIds.Add(hubId);
        }
        else
        {
            _selectedHubIds.Remove(hubId);
        }

        // Update the filter to the current selection.
        GetListInput.HubIds = _selectedHubIds.ToList();
        await SearchEntitiesAsync();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await GetAgentsLookupAsync();
        await OnSelectAllHubsChanged(true);
    }

    protected override async Task OpenCreateModalAsync()
    {
        await GetAgentsLookupAsync();
        await base.OpenCreateModalAsync();
    }

    protected override async Task OpenEditModalAsync(AgentDto entity)
    {
        await GetAgentsLookupAsync();

        // Get the current agent's hub assignments before opening modal
        var agentHubs = await AppService.GetAgentHubsAsync(entity.Id);
        var user = AgentEntities.FirstOrDefault(ae => ae.Id == entity.AgentUserId);
        SearchedUser = user?.Name ?? string.Empty;

        await base.OpenEditModalAsync(entity);

        EditingEntity.HubIds = agentHubs.Select(h => h.Id).ToList();
        EditingEntity.AgentUserId = entity.AgentUserId;
    }

    protected override Task CloseEditModalAsync()
    {
        SearchedUser = "";
        return base.CloseEditModalAsync();
    }

    protected override async Task SearchEntitiesAsync()
    {
        CurrentPage = 1;
        await GetEntitiesAsync();
        await InvokeAsync(StateHasChanged);
    }

    protected virtual async Task OnSearchTextChanged(string? value)
    {
        GetListInput.Filter = value;
        await SearchEntitiesAsync();
    }

    private async Task GetAgentsLookupAsync()
    {
        var agentList = await AppService.GetAgentLookupAsync();
        AgentEntities = agentList.Items;

        var hubResult = await AppService.GetHubLookupAsync();
        Hubs = hubResult.Items;
    }

}