@using Microsoft.Extensions.Localization
@using MboraMaxNext.Localization

<div class="text-center p-5">
    <div class="mb-4">
        <div class="d-inline-flex align-items-center justify-content-center rounded-circle" 
             style="padding: 2rem; background-color: #f0f0f0;">
            <Icon Name="@IconName" TextColor="TextColor.Secondary" IconSize="IconSize.x4" />
        </div>
    </div>
    <h4 class="mb-2">@Title</h4>
    <p class="text-muted mb-4">@Description</p>
    @if (ShowCallToAction && OnCallToAction.HasDelegate)
    {
        <Button Color="Color.Primary" Clicked="OnCallToAction">
            @CallToActionText
        </Button>
    }
</div>

@code {
    [Parameter]
    public Blazorise.IconName IconName { get; set; } = IconName.Mail;

    [Parameter]
    public string Title { get; set; } = "";

    [Parameter]
    public string Description { get; set; } = "";

    [Parameter]
    public bool ShowCallToAction { get; set; }

    [Parameter]
    public string CallToActionText { get; set; } = "";

    [Parameter]
    public EventCallback OnCallToAction { get; set; }
}
