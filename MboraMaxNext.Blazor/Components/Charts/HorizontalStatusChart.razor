@namespace MboraMaxNext.Blazor.Components.Charts
@using MboraMaxNext.Localization
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<MboraMaxNextResource> L

<div class="mt-4">
    <div class="progress mb-4" style="height: 8px;">
        @foreach (var item in Items)
        {
            <div class="progress-bar @item.ColorClass" style="width: @GetPercentage(item.Value)%"
                 role="progressbar"></div>
        }
    </div>

    <div class="row">
        @foreach (var item in Items)
        {
            <div class="col-@(12 / Math.Min(Items.Count, 4)) mb-3">
                <div class="d-flex align-items-center">
                    <span class="@item.ColorClass"
                          style="margin-right: 0.5rem; height: 0.5rem; width: 0.5rem; border-radius: 999px;"></span>
                    <div>
                        <div>
                            @item.Label
                        </div>
                        <h4 class="mb-0">@item.Value.ToString("N0") <small
                                class="text-muted">@item.UnitLabel</small></h4>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@code {
    [Parameter]
    public List<ChartItem> Items { get; set; } = new();

    [Parameter]
    public int Total { get; set; }

    private double GetPercentage(int count)
    {
        return Total > 0
            ? (double)count / Total * 100
            : 0;
    }

    public class ChartItem
    {
        public string Label { get; set; }
        public int Value { get; set; }
        public string ColorClass { get; set; }
        public string UnitLabel { get; set; }
    }
}
