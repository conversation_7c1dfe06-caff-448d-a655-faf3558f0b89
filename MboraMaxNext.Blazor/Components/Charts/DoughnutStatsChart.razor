@namespace MboraMaxNext.Blazor.Components.Charts
@using MboraMaxNext.Localization
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<MboraMaxNextResource> L

<div class="d-flex @(Orientation == Orientation.Vertical ? "flex-column" : "flex-row flex-wrap")">
    <div class="text-center position-relative" style="margin-top: 1rem;">
        <Chart @ref="doughnutChart" Type="ChartType.Doughnut" TItem="double" Options="@chartOptions"
               Style="height: 12rem;"/>
        <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
            <h1 class="@(CenterTextClass ?? "display-4 mb-0")">@CenterText</h1>
        </div>
    </div>

    <div
        class="mt-2 pt-2 @(Orientation == Orientation.Vertical ? "text-center d-flex justify-content-center flex-wrap " : "d-flex flex-column justify-content-center")">
        @if (Labels != null && chartColors.Count > 0)
        {
            @for (var i = 0; i < Math.Min(Math.Min(DisplayLabels.Length, MaxLegendItems), chartColors.Count); i++)
            {
                <div class="text-center mx-2 mb-2 d-flex align-items-center">
                <span
                    style="margin-right: 0.5rem; background-color: @chartColors[i]; height: 0.5rem; width: 0.5rem; border-radius: 999px;"></span>
                    <span>@(i < MaxLegendItems - 1 || DisplayLabels.Length <= MaxLegendItems ? DisplayLabels[i] : L["Others"])</span>
                </div>
            }
        }
    </div>
</div>


@code {
    [Parameter] public string[] Labels { get; set; } = Array.Empty<string>();

    [Parameter] public double[] Values { get; set; } = Array.Empty<double>();

    [Parameter] public string ChartTitle { get; set; } = "";

    [Parameter] public string CenterText { get; set; } = "";
    
    [Parameter] public string? CenterTextClass { get; set; }

    [Parameter] public int MaxLegendItems { get; set; } = 4;

    [Parameter] public int MaxDisplayItems { get; set; } = 3;

    [Parameter] public Orientation Orientation { get; set; } = Orientation.Vertical;

    private Chart<double> doughnutChart;
    private List<string> chartColors = new();

    // Display labels derived from the statistics or fallback
    private string[] DisplayLabels => Labels?.Length > 0
        ? Labels
        : new[] { L["NoData"].Value };

    // Predefined colors for the chart
    private readonly string[] DefaultColors = new[]
    {
        "#4361ee", "#ef476f", "#adb5bd", "#fb8500",
        "#06d6a0", "#ffbe0b", "#7209b7", "#3a86ff",
        "#ff595e", "#8ac926", "#1982c4", "#6a4c93",
        "#f72585", "#4cc9f0", "#560bad", "#b5179e"
    };

    private ChartOptions chartOptions = new()
    {
        Responsive = true,
        MaintainAspectRatio = false,
        Plugins = new ChartPlugins
        {
            Legend = new ChartLegend
            {
                Display = false
            },
            Tooltips = new ChartTooltips
            {
                Enabled = true
            }
        }
    };

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            if (doughnutChart != null)
            {
                await UpdateChart();
            }
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (doughnutChart != null)
        {
            await UpdateChart();
        }
    }

    private async Task UpdateChart()
    {
        try
        {
            await doughnutChart.Clear();
            chartColors.Clear();

            if (Labels == null || Values == null || Labels.Length == 0 || Values.Length == 0)
            {
                // Provide default empty data
                chartColors.Add("#e9ecef");
                await doughnutChart.AddLabelsDatasetsAndUpdate(
                    [L["NoData"].Value],
                    new DoughnutChartDataset<double>
                    {
                        Label = ChartTitle,
                        Data = [1.0],
                        BackgroundColor = chartColors.ToArray(),
                        BorderColor = chartColors.ToArray(),
                        BorderWidth = 1
                    }
                );
                return;
            }

            // Determine if we need to consolidate data (for more than MaxDisplayItems items)
            var displayLabels = Labels;
            var displayData = Values.ToList();

            if (Labels.Length > MaxDisplayItems)
            {
                displayLabels = Labels.Take(MaxDisplayItems).Append(L["Others"].Value).ToArray();
                displayData = Values.Take(MaxDisplayItems).ToList();
                displayData.Add(Values.Skip(MaxDisplayItems).Sum());
            }

            // Generate appropriate number of colors
            for (var i = 0; i < displayLabels.Length; i++)
            {
                // Generate a random color if we run out of predefined colors
                chartColors.Add(i < DefaultColors.Length ? DefaultColors[i] : $"#{new Random().Next(0x1000000):X6}");
            }

            // Create dataset with matching colors
            var dataset = new DoughnutChartDataset<double>
            {
                Label = ChartTitle,
                Data = [..displayData.ToArray()],
                BackgroundColor = chartColors.ToArray(),
                BorderColor = chartColors.ToArray(),
                BorderWidth = 1
            };

            // Update the chart
            await doughnutChart.AddLabelsDatasetsAndUpdate(displayLabels, dataset);

            // Force a re-render to update the legend
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error updating doughnut chart: {ex.Message}");
            // In production, you might want to log this error
        }
    }

}
