@using System.ComponentModel.DataAnnotations
@using MboraMaxNext.Localization
@using MboraMaxNext.Services.Dtos.Administration.Groups
@using Microsoft.Extensions.Localization
@using Volo.Abp.AspNetCore.Components.Web
@using MboraMaxNext.Models
@using MboraMaxNext.Components.UI.Form
@using MboraMaxNext.Blazor.Components.Ui
@using MboraMaxNext.Services.Dtos.Administration.Hubs
@using MboraMaxNext.Services.Dtos.Administration.Services

@inject IStringLocalizer<MboraMaxNextResource> L
@inject AbpBlazorMessageLocalizerHelper<MboraMaxNextResource> LH

<FormStepper Tabs="Tabs" Model="Group" ValidatePage="ValidatePage" OnCancel="OnCancel" OnSave="OnSave">
    <ChildContent>
        <TabPanel Name="group">
            <Row>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Group:Name"]</FieldLabel>
                            <TextEdit @bind-Text="Group.GroupName" placeholder='@L["Group:Name"]'/>
                            <CustomValidationMessage For="@(() => Group.GroupName)"/>
                        </Field>
                    </Validation>
                </Column>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validations Model="@Group" Mode="ValidationMode.Auto">
                        <Field>
                            <FieldLabel>@L["Group:StartDate"]</FieldLabel>
                            <DateEdit TValue="DateTime" Date="Group.StartDate"
                                      DateChanged="OnStartDateChanged"/>
                            <CustomValidationMessage For="@(() => Group.StartDate)"/>
                        </Field>
                    </Validations>
                </Column>
            </Row>
            <Row>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Group:MboraPercentage"]</FieldLabel>
                            <NumericEdit TValue="float?" @bind-Value="@Group.MboraPercentage"/>
                            <CustomValidationMessage For="@(() => Group.MboraPercentage)"/>
                        </Field>
                    </Validation>
                </Column>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Group:Percentage"]</FieldLabel>
                            <NumericEdit TValue="float?" @bind-Value="@Group.GroupPercentage"/>
                            <CustomValidationMessage For="@(() => Group.GroupPercentage)"/>
                        </Field>
                    </Validation>
                </Column>
            </Row>
            <Row>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Group:Chair"]</FieldLabel>
                            <Select TValue="Guid" @bind-SelectedValue="@Group.ChairId">
                                <SelectItem Value="@((Guid?)null)">@L["None"]</SelectItem>
                                @foreach (var user in Users)
                                {
                                    <SelectItem Value="@user.Id">@user.Name</SelectItem>
                                }
                            </Select>
                            <CustomValidationMessage For="@(() => Group.ChairId)"/>
                        </Field>
                    </Validation>
                </Column>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Group:Secretary"]</FieldLabel>
                            <Select TValue="Guid" @bind-SelectedValue="@Group.SecretaryId">
                                <SelectItem Value="@((Guid?)null)">@L["None"]</SelectItem>
                                @foreach (var user in Users)
                                {
                                    <SelectItem Value="@user.Id">@user.Name</SelectItem>
                                }
                            </Select>
                            <CustomValidationMessage For="@(() => Group.SecretaryId)"/>
                        </Field>
                    </Validation>
                </Column>
            </Row>
            <Row>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Hub"]</FieldLabel>
                            <Select TValue="Guid" @bind-SelectedValue="@Group.HubId">
                                <SelectItem Value="@((Guid?)null)">@L["None"]</SelectItem>
                                @foreach (var hub in Hubs)
                                {
                                    <SelectItem Value="@hub.Id">@hub.HubName</SelectItem>
                                }
                            </Select>
                            <CustomValidationMessage For="@(() => Group.HubId)"/>
                        </Field>
                    </Validation>
                </Column>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Service"]</FieldLabel>
                            <Select TValue="Guid" @bind-SelectedValue="@Group.ServiceId">
                                <SelectItem Value="@((Guid?)null)">@L["None"]</SelectItem>
                                @foreach (var service in Services)
                                {
                                    <SelectItem Value="@service.Id">@service.Description</SelectItem>
                                }
                            </Select>
                            <CustomValidationMessage For="@(() => Group.ServiceId)"/>
                        </Field>
                    </Validation>
                </Column>
            </Row>
            <Row>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Group:MaxMembers"]</FieldLabel>
                            <NumericEdit TValue="int" @bind-Value="@Group.MaxGroupMembers"/>
                            <CustomValidationMessage For="@(() => Group.MaxGroupMembers)"/>
                        </Field>
                    </Validation>
                </Column>
            </Row>
        </TabPanel>
        <TabPanel Name="cycle">
            <Row>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validations Model="@Group" Mode="ValidationMode.Auto">
                        <Field>
                            <FieldLabel>@L["Group:CycleStart"]</FieldLabel>
                            <DateEdit TValue="DateTime" Date="Group.CycleStart"
                                      DateChanged="OnCycleStartChanged"/>
                            <CustomValidationMessage For="@(() => Group.CycleStart)"/>
                        </Field>
                    </Validations>
                </Column>
                @if (!IsCreate)
                {
                    <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                        <Validations Model="@Group" Mode="ValidationMode.Auto">
                            <Field>
                                <FieldLabel>@L["Group:CycleEnd"]</FieldLabel>
                                <DateEdit TValue="DateTime" Date="Group.CycleEnd" Disabled="true"/>
                                <CustomValidationMessage For="@(() => Group.CycleEnd)"/>
                            </Field>
                        </Validations>
                    </Column>
                }
            </Row>
            <Row>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Group:CycleMonths"]</FieldLabel>
                            <NumericEdit TValue="int?" @bind-Value="@Group.CycleMonths"/>
                            <CustomValidationMessage For="@(() => Group.CycleMonths)"/>
                        </Field>
                    </Validation>
                </Column>
                <Column ColumnSize="ColumnSize.Is6.OnDesktop">
                    <Validation MessageLocalizer="@LH.Localize">
                        <Field>
                            <FieldLabel>@L["Group:CycleNumber"]</FieldLabel>
                            <NumericEdit TValue="int?" @bind-Value="@Group.CycleNumber" Disabled="IsCreate"/>
                            <CustomValidationMessage For="@(() => Group.CycleNumber)"/>
                        </Field>
                    </Validation>
                </Column>
            </Row>
        </TabPanel>
    </ChildContent>
</FormStepper>

@code {
    [Parameter] public bool IsCreate { get; set; } = true;

    [Parameter] public CreateUpdateGroupDto Group { get; set; } = new() { };

    [Parameter] public EventCallback<CreateUpdateGroupDto> OnSave { get; set; }

    [Parameter] public EventCallback OnCancel { get; set; }

    [Parameter] public IEnumerable<HubLookupDto> Hubs { get; set; } = new List<HubLookupDto>();

    [Parameter] public IEnumerable<UserLookupDto> Users { get; set; } = new List<UserLookupDto>();

    [Parameter] public IEnumerable<ServiceLookupDto> Services { get; set; } = new List<ServiceLookupDto>();

    private TabItem[] Tabs =
    [
        new("group", "Group"),
        new("cycle", "Cycle"),
    ];

    private Task<bool> ValidatePage(string selectedStep)
    {
        var validationContext = new ValidationContext(Group);
        var validationResults = new List<ValidationResult>();

        var isValid = selectedStep switch
        {
            "group" => Validator.TryValidateProperty(Group.GroupName, new ValidationContext(Group)
            {
                MemberName = nameof(Group.GroupName)
            }, validationResults) && Validator.TryValidateProperty(Group.StartDate, new ValidationContext(Group)
            {
                MemberName = nameof(Group.StartDate)
            }, validationResults) && Validator.TryValidateProperty(Group.HubId, new ValidationContext(Group)
            {
                MemberName = nameof(Group.HubId)
            }, validationResults),
            "cycle" => true,
            _ => true
        };

        return Task.FromResult(isValid);
    }

    private void OnStartDateChanged(DateTime value)
    {
        Group.StartDate = value;
    }

    private void OnCycleStartChanged(DateTime value)
    {
        Group.CycleStart = value;
    }

}