@namespace MboraMaxNext.Blazor.Components.Dashboard
@using MboraMaxNext.Blazor.Models
@inject NavigationManager NavigationManager

<div class="card pos-card <EMAIL> shadow text-white @(CardModel.IsEnabled ? "cursor-pointer" : "disabled")"
     @onclick="HandleClick">
    <div class="pos-card-bg card-bg-image"></div>
    <div class="pos-card-icon">
        <img src="/images/mbora-icons/dark/@CardModel.Icon" alt="Icon" class="w-25 h-25" />
    </div>
    <div class="pos-card-title w-50">
        @CardModel.Title
    </div>
</div>

@code {
    [Parameter] public DashboardCardModel CardModel { get; set; }

    private void HandleClick()
    {
        if (!CardModel.IsEnabled)
        {
            return;
        }

        if (CardModel.Action != null)
        {
            CardModel.Action.Invoke();
        }
        else if (!string.IsNullOrEmpty(CardModel.NavigateUrl))
        {
            NavigationManager.NavigateTo(CardModel.NavigateUrl);
        }
    }
}
