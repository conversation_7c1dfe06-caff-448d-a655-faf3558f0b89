@typeparam TItem
<div class="header-filter">
    <label>@Label</label>
    <Select TValue="string" @bind-Value="selectedValue">
        @foreach (var item in FilterItems)
        {
            <option value="@GetItemValue(item)">@GetItemDisplay(item)</option>
        }
    </Select>
</div>

@code {
    [Parameter] public string Label { get; set; }
    [Parameter] public IEnumerable<TItem> FilterItems { get; set; }
    [Parameter] public List<Guid> SelectedItems { get; set; }
    [Parameter] public EventCallback<(Guid id, bool selected)> OnSelectionChanged { get; set; }
    
    private string selectedValue;
    private static string? GetItemDisplay(TItem item)
    {
        // Adjust property as needed (e.g. "Name")
        var property = item?.GetType().GetProperty("Name");
        return property?.GetValue(item)?.ToString();
    }
    private static Guid GetItemValue(TItem item)
    {
        // Adjust property as needed (e.g. "Id")
        var property = item?.GetType().GetProperty("Id");
        return (Guid)(property?.GetValue(item) ?? Guid.Empty);
    }
}
