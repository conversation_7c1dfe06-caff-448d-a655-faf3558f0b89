@using Blazorise.Charts
@using MboraMaxNext.Services.Dtos.Administration.Members
@using MboraMaxNext.Localization
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<MboraMaxNextResource> L

<Card Shadow="Shadow.Small">
    <CardHeader>
        <CardTitle>@L["Occupations"]</CardTitle>
    </CardHeader>
    <CardBody>
        <div style="height: 240px;">
            <Chart @ref="genderChart" Type="ChartType.Doughnut" TItem="double" Options="@chartOptions"/>
        </div>

        <div class="d-flex justify-content-center mt-3">
            <div class="d-flex align-items-center me-4">
                <span class="badge bg-primary me-2" style="width: 12px; height: 12px;"></span>
                <span>@L["Male"]: @Statistics?.MaleCount.ToString("N0") (@GetPercentage(Statistics?.MaleCount ?? 0)%)</span>
            </div>
            <div class="d-flex align-items-center">
                <span class="badge bg-danger me-2" style="width: 12px; height: 12px;"></span>
                <span>@L["Female"]: @Statistics?.FemaleCount.ToString("N0") (@GetPercentage(Statistics?.FemaleCount ?? 0)%)</span>
            </div>
        </div>
    </CardBody>
</Card>

@code {
    [Parameter] public MemberStatisticsDto Statistics { get; set; }

    private Chart<double> genderChart;

    private ChartOptions chartOptions = new()
    {
        Responsive = true,
        MaintainAspectRatio = false,
        Plugins = new ChartPlugins
        {
            Legend = new ChartLegend
            {
                Display = false
            },
            Tooltips = new ChartTooltips
            {
                Enabled = true
            }
        }
    };

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        await UpdateChart();
    }

    private async Task UpdateChart()
    {
        await genderChart.Clear();

        await genderChart.AddLabelsDatasetsAndUpdate(
            Statistics?.OccupationLabels ?? [],
            new DoughnutChartDataset<double>
            {
                Label = L["Occupations"],
                Data = Statistics?.OccupationCounts ?? [],
                BackgroundColor = new[] { "#4361ee", "#ef476f" },
                BorderColor = new[] { "#4361ee", "#ef476f" },
                BorderWidth = 1
            }
        );
    }

    private string GetPercentage(int count)
    {
        if (Statistics?.TotalMembers == 0) return "0";

        if (Statistics == null) return "0%";
        var percentage = (double)count / Statistics.TotalMembers * 100;
        return percentage.ToString("0.0");
    }

}