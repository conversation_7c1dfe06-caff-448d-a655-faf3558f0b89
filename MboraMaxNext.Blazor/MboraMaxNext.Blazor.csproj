<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>MboraMaxNext</RootNamespace>
    <BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
  </PropertyGroup>

	<ItemGroup>
    <PackageReference Include="Blazor-ApexCharts" Version="5.1.0" />
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.6.2" />
    <PackageReference Include="Blazorise.Charts" Version="1.6.2" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.6.2" />
    <PackageReference Include="Blazorise.Components" Version="1.6.2" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.0.0" />
	</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Autofac.WebAssembly" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXLiteTheme" Version="4.0.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.Blazor.WebAssembly" Version="9.0.1" />
    <PackageReference Include="Volo.Abp.Identity.HttpApi.Client" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.TenantManagement.Blazor.WebAssembly" Version="9.0.1" />
    <PackageReference Include="Volo.Abp.TenantManagement.HttpApi.Client" Version="9.0.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.Domain.Shared" Version="9.0.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.HttpApi.Client" Version="9.0.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.SettingManagement.Blazor.WebAssembly" Version="9.0.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi.Client" Version="9.0.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Blazor.WebAssembly" Version="9.0.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi.Client" Version="9.0.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.FeatureManagement.Blazor.WebAssembly" Version="9.0.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi.Client" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MboraMaxNext.Contracts\MboraMaxNext.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="$(UserProfile)\.nuget\packages\*\*\contentFiles\any\*\*.abppkg*" />
  </ItemGroup>

</Project>