{"id": "a7011d42-2d08-4707-8d11-3e91144e40fe", "template": "app-nolayers", "versions": {"AbpFramework": "9.0.1", "AbpStudio": "0.9.15", "TargetDotnetFramework": "net9.0"}, "modules": {"MboraMaxNext": {"path": "MboraMaxNext.abpmdl"}}, "runProfiles": {"Default": {"path": "etc/run-profiles/Default.abprun.json"}}, "options": {"httpRequests": {"ignoredUrls": ["^/metrics$"]}}, "creatingStudioConfiguration": {"template": "app-nolayers", "createdAbpStudioVersion": "0.9.15", "multiTenancy": "true", "uiFramework": "blazor", "databaseProvider": "ef", "databaseManagementSystem": "postgresql", "theme": "leptonx-lite", "themeStyle": "", "progressiveWebApp": "", "publicWebsite": "", "socialLogin": ""}}