/*! DataTables Bootstrap 5 integration
 * © SpryMedia Ltd - datatables.net/license
 */
import jQuery from"jquery";import DataTable from"datatables.net";let $=jQuery;$.extend(!0,DataTable.defaults,{renderer:"bootstrap"}),$.extend(!0,DataTable.ext.classes,{container:"dt-container dt-bootstrap5",search:{input:"form-control form-control-sm"},length:{select:"form-select form-select-sm"},processing:{container:"dt-processing card"},layout:{row:"row mt-2 justify-content-between",cell:"d-md-flex justify-content-between align-items-center",tableCell:"col-12",start:"dt-layout-start col-md-auto me-auto",end:"dt-layout-end col-md-auto ms-auto",full:"dt-layout-full col-md"}}),DataTable.ext.renderer.pagingButton.bootstrap=function(t,e,a,o,n){var l=["dt-paging-button","page-item"],o=(o&&l.push("active"),n&&l.push("disabled"),$("<li>").addClass(l.join(" ")));return{display:o,clicker:$("<button>",{class:"page-link",role:"link",type:"button"}).html(a).appendTo(o)}},DataTable.ext.renderer.pagingContainer.bootstrap=function(t,e){return $("<ul/>").addClass("pagination").append(e)};export default DataTable;