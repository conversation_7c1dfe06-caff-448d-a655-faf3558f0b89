# Javascript Node CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-javascript/ for more details
#
version: 2
jobs:
  build:
    docker:
      # specify the version you desire here
      - image: circleci/node:8

      # Specify service dependencies here if necessary
      # CircleCI maintains a library of pre-built images
      # documented at https://circleci.com/docs/2.0/circleci-images/
      # - image: circleci/mongo:3.4.4

    working_directory: ~/repo

    steps:
      - checkout

      - run:
          name: "Checking Versions"
          command: |
            node --version
            npm --version

      - run:
          name: update-npm
          command: 'sudo npm install -g npm@latest'

      # Download and cache dependencies
      - restore_cache:
          key: dependency-cache-{{ checksum "package.json" }}

      - run: npm install

      - save_cache:
          key: dependency-cache-{{ checksum "package.json" }}
          paths:
            - ./node_modules

      - run:
          name: "Checking Versions"
          command: |
            node --version
            npm --version

      # run tests!
      - run: grunt test
