{"version": "9.0.1", "name": "@abp/jquery-validation", "repository": {"type": "git", "url": "https://github.com/abpframework/abp.git", "directory": "npm/packs/jquery-validation"}, "publishConfig": {"access": "public"}, "dependencies": {"@abp/jquery": "~9.0.1", "jquery-validation": "^1.21.0"}, "gitHead": "bb4ea17d5996f01889134c138d00b6c8f858a431", "homepage": "https://abp.io", "license": "LGPL-3.0", "keywords": ["aspnetcore", "boilerplate", "framework", "web", "best-practices", "angular", "maui", "blazor", "mvc", "csharp", "webapp"]}