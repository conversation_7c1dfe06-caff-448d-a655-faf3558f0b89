{"version": "9.0.1", "name": "@abp/timeago", "repository": {"type": "git", "url": "https://github.com/abpframework/abp.git", "directory": "npm/packs/timeago"}, "publishConfig": {"access": "public"}, "dependencies": {"@abp/jquery": "~9.0.1", "timeago": "^1.6.7"}, "gitHead": "bb4ea17d5996f01889134c138d00b6c8f858a431", "homepage": "https://abp.io", "license": "LGPL-3.0", "keywords": ["aspnetcore", "boilerplate", "framework", "web", "best-practices", "angular", "maui", "blazor", "mvc", "csharp", "webapp"]}