## ℹ️ Description

ABP Framework is a complete open-source infrastructure to create modern web applications by following the best practices and conventions of software development. This package is a part of the [ABP Framework](https://abp.io) and contains client-side files. 
For more information, check out the below links: 

🔗Official Website: https://abp.io

🔗Commercial Website: https://commercial.abp.io

🔗Commercial Demo: https://commercial.abp.io/demo

🔗GitHub Repository: https://github.com/abpframework/abp

🔗Official Theme: https://www.LeptonTheme.com

🔗Documentation: https://docs.abp.io

🔗Community: https://community.abp.io

🔗Blog: https://blog.abp.io

🔗Books: https://abp.io/books

🔗Twitter: https://twitter.com/abpframework

🔗Discord: https://community.abp.io/discord

🔗Stackoverflow: https://stackoverflow.com/questions/tagged/abp

🔗YouTube: https://www.youtube.com/@Volosoft


## 🤔 Why ABP Platform?

Why should you use the ABP.IO Platform instead of creating a new solution from scratch?

You can find the answer here 👉🏻 [Why ABP Platform?](https://docs.abp.io/en/commercial/latest/why-abp-io-platform)

## 🚀 Key Features of the ABP Framework

🟡 Modularity

🟡 Multi-Tenancy

🟡 Bootstrap Tag Helpers

🟡 Dynamic Forms

🟡 Authentication

🟡 Authorization

🟡 Distributed Event Bus

🟡 BLOB Storing

🟡 Text Templating

🟡 Tooling: ABP CLI

🟡 Cross-Cutting Concerns

🟡 Bundling & Minification

🟡 Virtual File System

🟡 Theming

🟡 Background Jobs

🟡 DDD Infrastructure

🟡 Auto REST APIs

🟡 Dynamic Client Proxies

🟡 Multiple Database Providers

🟡 Data filtering

🟡 Test Infrastructure

🟡 Audit Logging

🟡 Object to Object Mapping

🟡 Email & SMS Abstractions

🟡 Localization

🟡 Setting Management

🟡 Extension Methods

🟡 Aspect Oriented Programming

🟡 Dependency Injection


## 🧐 How It Works?

The following page explains how you use the ABP.IO Platform as a .NET developer 👉 [How it works?](https://commercial.abp.io/how-it-works)


### 📘 Supported Database Providers

🔵 Entity Framework Core

🔵 MongoDB

🔵 Dapper


### 🎴 Supported UI Frameworks

🔵 Angular

🔵 Razor Pages

🔵 Blazor Web Assembly

🔵 Blazor Server

🔵 MAUI with Blazor Hybrid


## 📫 Bug & Support

Support for open-source ABP Framework client-side packages is available at [GitHub Issues](https://github.com/abpframework/abp/issues), and the commercial support is available at [support.abp.io](https://support.abp.io).
