{"version": "9.0.1", "name": "@abp/bootstrap-datepicker", "repository": {"type": "git", "url": "https://github.com/abpframework/abp.git", "directory": "npm/packs/bootstrap-datepicker"}, "publishConfig": {"access": "public"}, "dependencies": {"bootstrap-datepicker": "^1.10.0"}, "gitHead": "bb4ea17d5996f01889134c138d00b6c8f858a431", "homepage": "https://abp.io", "license": "LGPL-3.0", "keywords": ["aspnetcore", "boilerplate", "framework", "web", "best-practices", "angular", "maui", "blazor", "mvc", "csharp", "webapp"]}