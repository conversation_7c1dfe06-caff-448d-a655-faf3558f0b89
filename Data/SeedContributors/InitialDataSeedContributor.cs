using Bogus;
using MboraMaxNext.Entities;
using MboraMaxNext.Entities.Administration;
using MboraMaxNext.Entities.Administration.Hubs;
using MboraMaxNext.Entities.Locations;
using MboraMaxNext.Entities.Products;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.Uow;

namespace MboraMaxNext.Data.SeedContributors;

public class InitialDataSeedContributor(
    IRepository<Member, Guid> memberRepository,
    IRepository<Hub, Guid> hubRepository,
    IRepository<Village, Guid> villageRepository,
    IRepository<Occupation, Guid> occupationRepository,
    IRepository<Service, Guid> serviceRepository,
    IRepository<Agent, Guid> agentRepository,
    IRepository<AgentHub, Guid> agentHubRepository,
    IRepository<Group, Guid> groupRepository,
    IRepository<VisitorLog, Guid> visitorLogRepository,
    IRepository<GroupMember> groupMembersRepository,
    IRepository<Product, Guid> productRepository,
    IRepository<Inventory, Guid> inventoryRepository,
    IIdentityRoleRepository roleRepository,
    IdentityUserManager userManager,
    ILogger<InitialDataSeedContributor> logger,
    IUnitOfWorkManager unitOfWorkManager,
    IGuidGenerator guidGenerator)
    : IDataSeedContributor, ITransientDependency
{
    private int _currentMboraId = 100000; // Starting MboraId

    private async Task PatchExistingMembersAsync()
    {
        logger.LogInformation("Starting to patch existing members without VillageId...");

        var membersWithoutVillage = await memberRepository.GetQueryableAsync();
        membersWithoutVillage = membersWithoutVillage.Where(m => m.VillageId == Guid.Empty);

        var members = await membersWithoutVillage.ToListAsync();
        if (members.Count == 0)
        {
            logger.LogInformation("No members found needing VillageId patch.");
            return;
        }

        foreach (var member in members)
        {
            var hub = await hubRepository.GetAsync(member.HubId);
            var village = await villageRepository.FindAsync(hub.VillageId);
            if (village == null) continue;
            member.VillageId = village.Id;
            await memberRepository.UpdateAsync(member);
            logger.LogInformation("Updated member {MemberId} with VillageId {VillageId}", member.Id, village.Id);
        }

        logger.LogInformation("Completed patching {Count} members.", members.Count);
    }

    public async Task SeedAsync(DataSeedContext context)
    {
        // Skip if any data already exists
        if (await AnyDataExistsAsync())
        {
            logger.LogInformation("Data already exists - skipping seeding");
            return;
        }

        using var uow = unitOfWorkManager.Begin(requiresNew: true);

        try
        {
            logger.LogInformation("Starting initial data seeding...");

            // Step 1: Create roles
            var managerRole = await CreateRoleAsync("Manager", context?.TenantId);
            var memberRole = await CreateRoleAsync("Member", context?.TenantId);
            var agentRole = await CreateRoleAsync("Agent", context?.TenantId);

            // Step 2: Seed villages
            var villages = await SeedVillagesAsync(context?.TenantId);

            // Step 3: Seed occupations
            var occupations = await SeedOccupationsAsync(context?.TenantId);

            // Step 4: Seed services
            var services = await SeedServicesAsync(context?.TenantId);

            // Step 5: Create managers and hubs
            var managers = await CreateManagersAsync(managerRole, context?.TenantId);
            var hubs = await CreateHubsAsync(villages, managers, context?.TenantId);

            // Step 6: Create members synchronously
            logger.LogInformation("Creating members synchronously...");
            var members = await CreateMembersAsync(memberRole, occupations, hubs, context?.TenantId);

            // Step 7: Create agents and assign them to hubs
            await CreateAgentsAsync(agentRole, hubs, context?.TenantId);

            // Step 8: Create groups
            await CreateGroups(members, services, context?.TenantId);

            // Step: 9: Create visitor logs
            await CreateVisitorLogsAsync(members, hubs, services, context?.TenantId);

            // Step 10: Create products
            var products = await SeedProductsAsync(context?.TenantId);

            // Step 11: Create inventory items for each hub
            await SeedInventoryAsync(products, hubs, context?.TenantId);

            // Patch any existing members that don't have VillageId set
            await PatchExistingMembersAsync();

            await uow.CompleteAsync();
            logger.LogInformation("Successfully completed initial data seeding.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during initial data seeding");
            await uow.RollbackAsync();
            throw;
        }
    }

    private async Task<bool> AnyDataExistsAsync()
    {
        return await memberRepository.GetCountAsync() > 0 ||
               await hubRepository.GetCountAsync() > 0 ||
               await villageRepository.GetCountAsync() > 0 ||
               await occupationRepository.GetCountAsync() > 0 ||
               await serviceRepository.GetCountAsync() > 0 ||
               await agentRepository.GetCountAsync() > 0 ||
               await productRepository.GetCountAsync() > 0 ||
               await inventoryRepository.GetCountAsync() > 0;
    }

    private async Task<IdentityRole> CreateRoleAsync(string roleName, Guid? tenantId)
    {
        var role = await roleRepository.FindByNormalizedNameAsync(roleName.ToUpper());
        if (role == null)
        {
            role = new IdentityRole(guidGenerator.Create(), roleName, tenantId)
            {
                IsStatic = true,
                IsPublic = true
            };
            await roleRepository.InsertAsync(role);
            logger.LogInformation("Created {RoleName} role", roleName);
        }

        return role;
    }

    private async Task<List<Village>> SeedVillagesAsync(Guid? tenantId)
    {
        var villages = new List<Village>();
        var defaultVillages = new[]
        {
            new { Name = "Mangochi Village", District = District.Mangochi },
            new { Name = "Zomba Village", District = District.Zomba },
            new { Name = "Blantyre Village", District = District.Blantyre }
        };

        foreach (var v in defaultVillages)
        {
            villages.Add(await villageRepository.InsertAsync(
                new Village(tenantId)
                {
                    VillageName = v.Name,
                    VillageDistrict = v.District
                }
            ));
        }

        logger.LogInformation("Created {Count} villages", villages.Count);
        return villages;
    }

    private async Task<List<Occupation>> SeedOccupationsAsync(Guid? tenantId)
    {
        var occupations = new List<Occupation>();
        var defaultOccupations = new[]
        {
            "Farmer", "Trader", "Teacher", "Healthcare Worker",
            "Business Owner", "Craftsperson", "Driver",
            "Market Vendor", "Construction Worker", "Domestic Worker"
        };

        foreach (var name in defaultOccupations)
        {
            occupations.Add(await occupationRepository.InsertAsync(
                new Occupation(tenantId) { Description = name }
            ));
        }

        logger.LogInformation("Created {Count} occupations", occupations.Count);
        return occupations;
    }

    private async Task<List<Service>> SeedServicesAsync(Guid? tenantId)
    {
        var services = new List<Service>();
        var defaultServices = new[]
        {
            new { Description = "VSL", Price = 100.00M },
            new { Description = "Clinic", Price = 500.00M },
            new { Description = "Agricultural Support", Price = 250.00M },
            new { Description = "Business Advisory", Price = 300.00M }
        };

        foreach (var service in defaultServices)
        {
            services.Add(await serviceRepository.InsertAsync(
                new Service(tenantId)
                {
                    Description = service.Description,
                    ServicePrice = service.Price
                }
            ));
        }

        logger.LogInformation("Created {Count} services", defaultServices.Length);
        return services;
    }

    private async Task<List<IdentityUser>> CreateManagersAsync(IdentityRole managerRole, Guid? tenantId)
    {
        var managers = new List<IdentityUser>();

        for (var i = 0; i < 3; i++)
        {
            var email = $"manager{i + 1}@mbora.org";
            var manager = new IdentityUser(
                guidGenerator.Create(),
                email,
                email,
                tenantId
            )
            {
                Name = $"Manager {i + 1}",
                Surname = "Test"
            };

            var result = await userManager.CreateAsync(manager, "1q2w3E*");
            if (!result.Succeeded)
            {
                throw new Exception(
                    $"Manager creation failed: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }

            result = await userManager.AddToRoleAsync(manager, managerRole.Name);
            if (!result.Succeeded)
            {
                throw new Exception(
                    $"Adding manager to role failed: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }

            managers.Add(manager);
            logger.LogInformation("Created manager {Email}", email);
        }

        return managers;
    }

    private async Task<List<Hub>> CreateHubsAsync(List<Village> villages, List<IdentityUser> managers, Guid? tenantId)
    {
        var hubs = new List<Hub>();
        for (var i = 0; i < Math.Min(villages.Count, managers.Count); i++)
        {
            var hub = new Hub(tenantId)
            {
                HubName = $"{villages[i].VillageName} Hub",
                HubCode = $"HUB{i + 1}",
                VillageId = villages[i].Id,
                ManagerId = managers[i].Id,
                HubAddress = "///something.is.here",
                HubEmail = $"hub-{i}@mbora.org",
                HubTraditionalAuthority = "Mbora Authority"
            };

            var createdHub = await hubRepository.InsertAsync(hub);
            hubs.Add(createdHub);
            logger.LogInformation("Created hub {HubCode}", hub.HubCode);
        }

        return hubs;
    }

    private async Task<List<Member>> CreateMembersAsync(IdentityRole memberRole, List<Occupation> occupations,
        List<Hub> hubs, Guid? tenantId)
    {
        logger.LogInformation("Starting member creation process...");
        var faker = new Faker();
        var memberUsers = new List<IdentityUser>();
        var members = new List<Member>();

        // Create a distribution strategy for occupations to ensure diversity
        var occupationDistribution = new Dictionary<Guid, int>();
        foreach (var occupation in occupations)
        {
            occupationDistribution[occupation.Id] = 0;
        }

        // Create fake data generators
        var fakeMember = new Faker<Member>()
            .CustomInstantiator(f => new Member(tenantId))
            .RuleFor(m => m.Title, f => f.Random.Enum<Title>())
            .RuleFor(m => m.OtherInitials, f => f.Random.AlphaNumeric(1))
            .RuleFor(m => m.Gender, f => f.Random.Enum<Gender>())
            .RuleFor(m => m.IdentificationNumber, f => f.Random.Replace("########"))
            .RuleFor(m => m.DateOfBirth, f => f.Date.Past(50, DateTime.Now.AddYears(-18)))
            .RuleFor(m => m.AnnualIncome, f => f.Random.Decimal(50000, 5000000))
            .RuleFor(m => m.Telephone,
                f => $"+265{f.Random.ArrayElement(["88", "89", "11", "99", "98"])}{f.Random.Replace("#######")}")
            .RuleFor(m => m.MemberWallet, f => f.Random.Decimal(0, 50000))
            .RuleFor(m => m.What3Words,
                f =>
                    $"///{FormatString(f.Random.Word())}.{FormatString(f.Random.Word())}.{FormatString(f.Random.Word())}")
            .RuleFor(m => m.NextOfKin, f => f.Name.FullName())
            .RuleFor(m => m.MaritalStatus, f => f.Random.Enum<MaritalStatus>())
            .RuleFor(m => m.CriminalRecord, f => f.Random.Enum<CriminalRecord>())
            .RuleFor(m => m.LiteracyLevel, f => f.Random.Enum<LiteracyLevel>())
            .RuleFor(m => m.Coordinates, f => $"{f.Address.Latitude()}, {f.Address.Longitude()}")
            .RuleFor(m => m.RegistrationDate, f => f.Date.Past(2))
            .RuleFor(m => m.Temporary, f => f.Random.Bool(0.1f))
            .RuleFor(m => m.IsLoanOngoing, f => f.Random.Bool(0.2f))
            .RuleFor(m => m.Status, f => f.Random.Enum<MemberStatus>())
            .RuleFor(m => m.Registered, _ => true);

        // Generate all users and members in memory
        const int totalMembers = 150;
        for (var i = 0; i < totalMembers; i++)
        {
            var member = fakeMember.Generate();
            var hub = faker.PickRandom(hubs);
            var userId = guidGenerator.Create();
            var email = faker.Internet.Email();
            var memberUser = new IdentityUser(
                userId,
                email,
                email,
                tenantId
            )
            {
                Name = faker.Name.FirstName(),
                Surname = faker.Name.LastName(),
            };
            memberUser.SetPhoneNumber(member.Telephone, true);
            memberUsers.Add(memberUser);

            member.UserId = userId;
            member.HubId = hub.Id;
            member.VillageId = hub.VillageId; // Set VillageId from Hub

            // Select occupation based on current distribution
            // Find the occupation with the least members assigned
            var occupationId = occupationDistribution
                .OrderBy(kv => kv.Value)
                .First().Key;

            // Increment the count for this occupation
            occupationDistribution[occupationId]++;

            member.OccupationId = occupationId;
            member.MboraId = _currentMboraId++;
            members.Add(member);
        }

        try
        {
            // Bulk insert identity users
            logger.LogInformation("Inserting {Count} identity users...", memberUsers.Count);
            foreach (var user in memberUsers)
            {
                var result = await userManager.CreateAsync(user, "Member123*");
                if (!result.Succeeded)
                {
                    throw new Exception(
                        $"Failed to create user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }

                result = await userManager.AddToRoleAsync(user, memberRole.Name);
                if (!result.Succeeded)
                {
                    throw new Exception(
                        $"Failed to add user to role: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }
            }

            // Bulk insert members
            logger.LogInformation("Inserting {Count} members...", members.Count);
            var insertedMembers = new List<Member>();

            foreach (var member in members)
            {
                var insertedMember = await memberRepository.InsertAsync(member);
                insertedMembers.Add(insertedMember);
            }

            logger.LogInformation("Successfully created {Count} members with their identity users", members.Count);

            return insertedMembers;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create members and users");
            throw;
        }
    }

    private async Task CreateAgentsAsync(IdentityRole agentRole, List<Hub> hubs, Guid? tenantId)
    {
        logger.LogInformation("Creating agent users...");
        var agents = new List<IdentityUser>();

        // Create 5 agents
        for (var i = 0; i < 5; i++)
        {
            var email = $"agent{i + 1}@mbora.org";
            var agentUser = new IdentityUser(
                guidGenerator.Create(),
                email,
                email,
                tenantId
            )
            {
                Name = $"Agent {i + 1}",
                Surname = "Test",
            };

            var result = await userManager.CreateAsync(agentUser, "1q2w3E*");
            if (!result.Succeeded)
            {
                throw new Exception(
                    $"Agent user creation failed: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }

            result = await userManager.AddToRoleAsync(agentUser, agentRole.Name);
            if (!result.Succeeded)
            {
                throw new Exception(
                    $"Adding agent to role failed: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }

            agents.Add(agentUser);
            logger.LogInformation("Created agent user {Email}", email);
        }

        logger.LogInformation("Creating agent records and assigning hubs...");
        var random = new Random();

        for (var i = 0; i < agents.Count; i++)
        {
            var agentUser = agents[i];
            var agent = new Agent(tenantId)
            {
                AgentCode = $"AGT{i:D3}",
                AgentUserId = agentUser.Id,
                AgentType = AgentType.Commission,
                MaximumTransactionAmount = 100,
                AgentCommissionRate = 10,
                AgentCommissionValue = 0
            };

            await agentRepository.InsertAsync(agent);

            // Assign 1-3 random hubs to this agent
            var hubCount = random.Next(1, 4);
            var selectedHubs = hubs.OrderBy(x => random.Next()).Take(hubCount).ToList();

            foreach (var hub in selectedHubs)
            {
                await agentHubRepository.InsertAsync(new AgentHub(tenantId)
                {
                    AgentId = agent.Id,
                    HubId = hub.Id,
                });
            }

            logger.LogInformation(
                "Created agent record for {Email} with code {AgentCode} and assigned {HubCount} hubs",
                agentUser.Email, agent.AgentCode, hubCount);
        }
    }

    public async Task CreateGroups(List<Member> members, List<Service> services, Guid? tenantId)
    {
        logger.LogInformation("Starting groups creation process...");
        var faker = new Faker();
        var groupCount = 0;
        var hubGroupedMembers = members.GroupBy(m => m.HubId).ToList();

        var fakeGroup = new Faker<Group>()
            .CustomInstantiator(f => new Group(tenantId))
            .RuleFor(m => m.GroupName, f => f.Company.CompanyName())
            .RuleFor(m => m.StartDate, f => f.Date.Past())
            .RuleFor(m => m.GroupPercentage, f => f.Random.Float() * 100)
            .RuleFor(m => m.MboraPercentage, f => f.Random.Float() * 100)
            .RuleFor(m => m.CycleStart, f => f.Date.Past())
            .RuleFor(m => m.CycleMonths, f => f.Random.Number())
            .RuleFor(m => m.CycleNumber, f => f.Random.Number())
            .RuleFor(m => m.CycleEnd, f => f.Date.Future())
            .RuleFor(m => m.ServiceId, f => f.PickRandom(services).Id);

        foreach (var hubGroupedMember in hubGroupedMembers)
        {
            var allMembers = hubGroupedMember.ToList();
            var newGroupMembers = new List<GroupMember>();
            for (var i = 0; i < 3; i++)
            {
                var group = fakeGroup.Generate();

                group.ChairId = faker.PickRandom(allMembers).UserId;
                group.SecretaryId = faker.PickRandom(allMembers).UserId;
                group.HubId = hubGroupedMember.Key;
                group.Status = GroupStatusEnum.Active;
                group.MaxGroupMembers = faker.Random.Int(allMembers.Count, allMembers.Count + 50);

                group = await groupRepository.InsertAsync(group);

                newGroupMembers.AddRange(allMembers.Select(member => new GroupMember(group.Id, member.Id)));

                groupCount++;
            }

            await groupMembersRepository.InsertManyAsync(newGroupMembers);
        }

        logger.LogInformation("Successfully created {Count} groups", groupCount);
    }


    private async Task CreateVisitorLogsAsync(List<Member> members, List<Hub> hubs, List<Service> services, Guid? tenantId)
    {
        logger.LogInformation("Starting visitor log creation process...");

        var faker = new Faker();
        var visitorLogs = new List<VisitorLog>();
        var today = DateTime.Today;

        var fakeVisitorLog = new Faker<VisitorLog>()
            .CustomInstantiator(f => new VisitorLog(tenantId))
            .RuleFor(v => v.Id, f => guidGenerator.Create())
            .RuleFor(v => v.VisitDate, f => f.Date.Recent(40, today.AddDays(-1)))
            .RuleFor(v => v.InTime, f => new TimeSpan(
                f.Random.Int(8, 16), // Hours between 8 AM and 4 PM
                f.Random.Int(0, 59), // Minutes
                0)) // No seconds
            .RuleFor(v => v.ExtraVisitData, f => GenerateVisitPurpose(f));

        logger.LogInformation("Generating visitor logs for {Count} members...", members.Count);

        foreach (var member in members)
        {
            // Generate 2-5 logs per member
            var logsCount = faker.Random.Int(2, 5);

            for (var i = 0; i < logsCount; i++)
            {
                var log = fakeVisitorLog.Generate();

                log.HubId = faker.Random.Bool(0.7f)
                    ? member.HubId
                    : faker.PickRandom(hubs).Id;

                log.MemberId = member.Id;
                log.ServiceId = faker.PickRandom(services).Id;

                if (faker.Random.Bool(0.9f))
                {
                    var durationMinutes = faker.Random.Int(15, 180);
                    log.OutTime = log.InTime.Add(TimeSpan.FromMinutes(durationMinutes));
                }

                log.CreationTime = log.VisitDate.Add(log.InTime);

                visitorLogs.Add(log);
            }
        }

        try
        {
            // Bulk insert visitor logs
            logger.LogInformation("Inserting {Count} visitor logs...", visitorLogs.Count);

            foreach (var batch in visitorLogs.Chunk(100)) // Process in batches of 100
            {
                await visitorLogRepository.InsertManyAsync(batch);
            }

            logger.LogInformation("Successfully created {Count} visitor logs", visitorLogs.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create visitor logs");
            throw;
        }
    }

    private string GenerateVisitPurpose(Faker faker)
    {
        var visitPurposes = new[]
        {
            "Attending monthly community meeting",
            "Collecting agricultural supplies",
            "Financial literacy training session",
            "Weekly savings group meeting",
            "Dropping off produce for market",
            "Health and wellness workshop",
            "Digital skills training",
            "Checking on loan application status",
            "Collecting information about new programs",
            "Meeting with hub manager about community project",
            "Business plan development session",
            "Picking up educational materials",
            "Agricultural technique demonstration",
            "Small business owners meetup",
            "Water management training"
        };

        var purpose = faker.PickRandom(visitPurposes);

        if (faker.Random.Bool(0.3f))
        {
            var additionalNotes = new[]
            {
                "Brought family member along",
                "Discussed upcoming village event",
                "Requested additional support",
                "Shared feedback about recent program",
                "Inquired about new opportunities",
                "Contributed to suggestion box",
                "Volunteered for community event"
            };

            purpose += ". " + faker.PickRandom(additionalNotes);
        }

        return purpose;
    }

    public static string FormatString(string input)
    {
        return input.Replace(" ", "").ToLower();
    }

    private async Task<List<Product>> SeedProductsAsync(Guid? tenantId)
    {
        logger.LogInformation("Starting product data seeding...");
        var products = new List<Product>();

        // Agricultural Products
        var agriculturalProducts = new[]
        {
            new { Name = "Maize Seeds", Unit = ProductUnit.Vegetable, Category = ProductCategory.Vegetable, Info = "High-quality maize seeds for planting", Price = 1500.00m },
            new { Name = "Tomato Seeds", Unit = ProductUnit.Vegetable, Category = ProductCategory.Vegetable, Info = "Hybrid tomato seeds", Price = 800.00m },
            new { Name = "Cabbage Seeds", Unit = ProductUnit.Vegetable, Category = ProductCategory.Vegetable, Info = "Drought-resistant cabbage seeds", Price = 950.00m },
            new { Name = "Onion Seeds", Unit = ProductUnit.Vegetable, Category = ProductCategory.Vegetable, Info = "Red onion seeds", Price = 750.00m },
            new { Name = "Carrot Seeds", Unit = ProductUnit.Vegetable, Category = ProductCategory.Vegetable, Info = "Orange carrot seeds", Price = 600.00m }
        };

        // Fertilizers
        var fertilizers = new[]
        {
            new { Name = "NPK Fertilizer", Unit = ProductUnit.Vegetable, Category = ProductCategory.Fertilizer, Info = "Balanced NPK 15-15-15 fertilizer", Price = 3500.00m },
            new { Name = "Urea Fertilizer", Unit = ProductUnit.Vegetable, Category = ProductCategory.Fertilizer, Info = "High nitrogen content", Price = 3200.00m },
            new { Name = "Organic Compost", Unit = ProductUnit.Vegetable, Category = ProductCategory.Fertilizer, Info = "Natural organic compost", Price = 2000.00m }
        };

        // Packaging Materials
        var packagingMaterials = new[]
        {
            new { Name = "Jute Bags", Unit = ProductUnit.Beverage, Category = ProductCategory.Packaging, Info = "Eco-friendly jute bags for produce", Price = 250.00m },
            new { Name = "Plastic Crates", Unit = ProductUnit.Beverage, Category = ProductCategory.Packaging, Info = "Durable plastic crates for transport", Price = 1200.00m },
            new { Name = "Paper Bags", Unit = ProductUnit.Beverage, Category = ProductCategory.Packaging, Info = "Biodegradable paper bags", Price = 150.00m }
        };

        // Textiles
        var textiles = new[]
        {
            new { Name = "Cotton Fabric", Unit = ProductUnit.Beverage, Category = ProductCategory.Textile, Info = "Locally produced cotton fabric", Price = 1800.00m },
            new { Name = "Wool Yarn", Unit = ProductUnit.Beverage, Category = ProductCategory.Textile, Info = "Natural wool yarn", Price = 2200.00m }
        };

        // Fruits
        var fruits = new[]
        {
            new { Name = "Mango Seedlings", Unit = ProductUnit.Fruit, Category = ProductCategory.Fruit, Info = "Grafted mango seedlings", Price = 1500.00m },
            new { Name = "Banana Suckers", Unit = ProductUnit.Fruit, Category = ProductCategory.Fruit, Info = "Disease-resistant banana suckers", Price = 800.00m }
        };

        // Herbs
        var herbs = new[]
        {
            new { Name = "Mint Plants", Unit = ProductUnit.Herb, Category = ProductCategory.Herb, Info = "Fresh mint plants", Price = 500.00m },
            new { Name = "Basil Seeds", Unit = ProductUnit.Herb, Category = ProductCategory.Herb, Info = "Aromatic basil seeds", Price = 600.00m }
        };

        // Beverages
        var beverages = new[]
        {
            new { Name = "Hibiscus Tea", Unit = ProductUnit.Beverage, Category = ProductCategory.Beverage, Info = "Dried hibiscus flowers for tea", Price = 1200.00m },
            new { Name = "Ginger Powder", Unit = ProductUnit.Beverage, Category = ProductCategory.Beverage, Info = "Dried and ground ginger", Price = 1500.00m }
        };

        // Combine all product categories
        var allProducts = agriculturalProducts
            .Concat(fertilizers)
            .Concat(packagingMaterials)
            .Concat(textiles)
            .Concat(fruits)
            .Concat(herbs)
            .Concat(beverages)
            .ToList();

        // Insert products
        foreach (var productData in allProducts)
        {
            var product = new Product(tenantId)
            {
                ProductName = productData.Name,
                ProductUnit = productData.Unit,
                ProductCategory = productData.Category,
                ProductInfo = productData.Info,
                ProductPrice = productData.Price,
                Available = true
            };

            products.Add(await productRepository.InsertAsync(product));
        }

        logger.LogInformation("Created {Count} products", products.Count);
        return products;
    }

    private async Task SeedInventoryAsync(List<Product> products, List<Hub> hubs, Guid? tenantId)
    {
        var faker = new Faker();

        foreach (var hub in hubs)
        {
            foreach (var product in products)
            {
                // Generate random quantity between 0 and 50
                var quantity = faker.Random.Int(0, 50);

                // Generate random threshold between 2 and 10
                var threshold = faker.Random.Int(2, 10);

                // Generate a unique inventory name for each tenant and hub
                var inventoryName = $"{product.ProductName}_{hub.HubName}_{tenantId}";

                if (!await inventoryRepository.AnyAsync(i => i.InventoryName == inventoryName && i.TenantId == tenantId))
                {
                    var inventory = new Inventory(tenantId)
                    {
                        InventoryName = inventoryName,
                        ProductId = product.Id,
                        HubId = hub.Id,
                        Quantity = quantity,
                        Threshold = threshold,


                    };

                    await inventoryRepository.InsertAsync(inventory);
                }
            }
        }
    }
}

