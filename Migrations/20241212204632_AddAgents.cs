using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MboraMaxNext.Migrations
{
    /// <inheritdoc />
    public partial class AddAgents : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppAgents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    AgentCode = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    AgentUserId = table.Column<Guid>(type: "uuid", nullable: false),
                    AgentType = table.Column<int>(type: "integer", nullable: false),
                    AgentCommissionRate = table.Column<int>(type: "integer", nullable: true),
                    AgentCommissionValue = table.Column<decimal>(type: "numeric(19,4)", nullable: true),
                    MaximumTransactionAmount = table.Column<decimal>(type: "numeric(19,4)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "text", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp without time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppAgents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppAgents_AbpUsers_AgentUserId",
                        column: x => x.AgentUserId,
                        principalTable: "AbpUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppAgents_AgentCode_TenantId",
                table: "AppAgents",
                columns: new[] { "AgentCode", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppAgents_AgentUserId",
                table: "AppAgents",
                column: "AgentUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppAgents");
        }
    }
}
