{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET9_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NET8_0_OR_GREATER", "NET9_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "13.0", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v9.0": {"MboraMaxNext.Host/1.0.0": {"dependencies": {"Bogus": "35.6.1", "ClosedXML": "0.104.2", "Humanizer.Core": "3.0.0-beta.54", "MboraMaxNext.Blazor": "1.0.0", "MboraMaxNext.Contracts": "1.0.0", "Microsoft.AspNetCore.Components.WebAssembly.Server": "9.0.0", "Microsoft.EntityFrameworkCore.Tools": "9.0.0", "Serilog.AspNetCore": "8.0.2", "Serilog.Sinks.Async": "2.0.0", "Volo.Abp.Account.Application": "9.0.1", "Volo.Abp.Account.HttpApi": "9.0.1", "Volo.Abp.Account.Web.OpenIddict": "9.0.1", "Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite": "4.0.1", "Volo.Abp.AspNetCore.Serilog": "9.0.1", "Volo.Abp.AuditLogging.EntityFrameworkCore": "9.0.1", "Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.Autofac": "9.0.1", "Volo.Abp.BackgroundJobs.EntityFrameworkCore": "9.0.1", "Volo.Abp.BlobStoring.Database.EntityFrameworkCore": "9.0.1", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.EntityFrameworkCore.PostgreSql": "9.0.1", "Volo.Abp.FeatureManagement.Application": "9.0.1", "Volo.Abp.FeatureManagement.EntityFrameworkCore": "9.0.1", "Volo.Abp.FeatureManagement.HttpApi": "9.0.1", "Volo.Abp.Identity.Application": "9.0.1", "Volo.Abp.Identity.EntityFrameworkCore": "9.0.1", "Volo.Abp.Identity.HttpApi": "9.0.1", "Volo.Abp.OpenIddict.EntityFrameworkCore": "9.0.1", "Volo.Abp.PermissionManagement.Application": "9.0.1", "Volo.Abp.PermissionManagement.Domain.Identity": "9.0.1", "Volo.Abp.PermissionManagement.Domain.OpenIddict": "9.0.1", "Volo.Abp.PermissionManagement.EntityFrameworkCore": "9.0.1", "Volo.Abp.PermissionManagement.HttpApi": "9.0.1", "Volo.Abp.SettingManagement.Application": "9.0.1", "Volo.Abp.SettingManagement.EntityFrameworkCore": "9.0.1", "Volo.Abp.SettingManagement.HttpApi": "9.0.1", "Volo.Abp.Studio.Client.AspNetCore": "0.9.15", "Volo.Abp.Swashbuckle": "9.0.1", "Volo.Abp.TenantManagement.Application": "9.0.1", "Volo.Abp.TenantManagement.EntityFrameworkCore": "9.0.1", "Volo.Abp.TenantManagement.HttpApi": "9.0.1", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.BearerToken": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization.Reference": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization.Reference": "*******", "Microsoft.AspNetCore.Components.Reference": "*******", "Microsoft.AspNetCore.Components.Endpoints": "*******", "Microsoft.AspNetCore.Components.Forms.Reference": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web.Reference": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal.Reference": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions": "*******", "Microsoft.AspNetCore.DataProtection": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions": "*******", "Microsoft.AspNetCore.Hosting": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http": "*******", "Microsoft.AspNetCore.Http.Extensions": "*******", "Microsoft.AspNetCore.Http.Features": "*******", "Microsoft.AspNetCore.Http.Results": "*******", "Microsoft.AspNetCore.HttpLogging": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata.Reference": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.OutputCaching": "*******", "Microsoft.AspNetCore.RateLimiting": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.RequestDecompression": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticAssets": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities.Reference": "*******", "Microsoft.CSharp": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine.Reference": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference": "*******", "Microsoft.Extensions.Configuration.FileExtensions.Reference": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json.Reference": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets.Reference": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.DependencyInjection.Reference": "*******", "Microsoft.Extensions.Diagnostics.Abstractions.Reference": "*******", "Microsoft.Extensions.Diagnostics.Reference": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.Features": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite.Reference": "*******", "Microsoft.Extensions.FileProviders.Embedded.Reference": "*******", "Microsoft.Extensions.FileProviders.Physical.Reference": "*******", "Microsoft.Extensions.FileSystemGlobbing.Reference": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http.Reference": "*******", "Microsoft.Extensions.Identity.Core.Reference": "*******", "Microsoft.Extensions.Identity.Stores": "*******", "Microsoft.Extensions.Localization.Abstractions.Reference": "*******", "Microsoft.Extensions.Localization.Reference": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration": "*******", "Microsoft.Extensions.Logging.Console": "*******", "Microsoft.Extensions.Logging.Debug": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop.Reference": "*******", "Microsoft.Net.Http.Headers.Reference": "*******", "Microsoft.VisualBasic.Core": "1*******", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "*******", "Microsoft.Win32.Registry": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext": "*******", "System.Buffers": "*******", "System.Collections.Concurrent": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console": "*******", "System.Core": "*******", "System.Data.Common": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.EventLog.Reference": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Asn1": "*******", "System.Formats.Tar": "*******", "System.Globalization.Calendars": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.AccessControl": "*******", "System.IO.FileSystem": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines.Reference": "*******", "System.IO.Pipes.AccessControl": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions.Reference": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable.Reference": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives": "*******", "System.Net.Quic": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors": "*******", "System.ObjectModel.Reference": "*******", "System.Reflection.DispatchProxy": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions.Reference": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles": "*******", "System.Runtime.InteropServices": "*******", "System.Runtime.InteropServices.JavaScript": "*******", "System.Runtime.InteropServices.RuntimeInformation": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader.Reference": "*******", "System.Runtime.Numerics": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms": "*******", "System.Security.Cryptography.Cng": "*******", "System.Security.Cryptography.Csp": "*******", "System.Security.Cryptography": "*******", "System.Security.Cryptography.Encoding": "*******", "System.Security.Cryptography.OpenSsl": "*******", "System.Security.Cryptography.Primitives": "*******", "System.Security.Cryptography.X509Certificates": "*******", "System.Security.Cryptography.Xml": "*******", "System.Security": "*******", "System.Security.Principal": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions": "*******", "System.Text.Encodings.Web.Reference": "*******", "System.Text.Json.Reference": "*******", "System.Text.RegularExpressions": "*******", "System.Threading.Channels.Reference": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.RateLimiting": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple.Reference": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "runtime": {"MboraMaxNext.Host.dll": {}}, "compile": {"MboraMaxNext.Host.dll": {}}}, "Asp.Versioning.Abstractions/8.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.8851.31619"}}, "compile": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {}}}, "Asp.Versioning.Http/8.1.0": {"dependencies": {"Asp.Versioning.Abstractions": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.8851.31627"}}, "compile": {"lib/net8.0/Asp.Versioning.Http.dll": {}}}, "Asp.Versioning.Mvc/8.1.0": {"dependencies": {"Asp.Versioning.Http": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.8851.31627"}}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.dll": {}}}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"dependencies": {"Asp.Versioning.Mvc": "8.1.0"}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.8851.31636"}}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {}}}, "AsyncKeyedLock/7.0.1": {"runtime": {"lib/net9.0/AsyncKeyedLock.dll": {"assemblyVersion": "7.0.1.0", "fileVersion": "7.0.1.0"}}, "compile": {"lib/net9.0/AsyncKeyedLock.dll": {}}}, "Autofac/8.1.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net8.0/Autofac.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Autofac.dll": {}}}, "Autofac.Extensions.DependencyInjection/10.0.0": {"dependencies": {"Autofac": "8.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.0.0"}}, "compile": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {}}}, "Autofac.Extras.DynamicProxy/7.1.0": {"dependencies": {"Autofac": "8.1.0", "Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.0.0"}}, "compile": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {}}}, "AutoMapper/13.0.1": {"dependencies": {"Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net6.0/AutoMapper.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.0"}}, "compile": {"lib/net6.0/AutoMapper.dll": {}}}, "Blazor-ApexCharts/5.1.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Http": "9.0.0"}, "runtime": {"lib/net8.0/Blazor-ApexCharts.dll": {"assemblyVersion": "5.1.0.0", "fileVersion": "5.1.0.0"}}, "compile": {"lib/net8.0/Blazor-ApexCharts.dll": {}}}, "Blazorise/1.6.2": {"dependencies": {"Blazorise.Licensing": "1.2.0", "Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Blazorise.dll": {"assemblyVersion": "1.6.2.0", "fileVersion": "1.6.2.0"}}, "compile": {"lib/net8.0/Blazorise.dll": {}}}, "Blazorise.Bootstrap5/1.6.2": {"dependencies": {"Blazorise": "1.6.2", "Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Blazorise.Bootstrap5.dll": {"assemblyVersion": "1.6.2.0", "fileVersion": "1.6.2.0"}}, "compile": {"lib/net8.0/Blazorise.Bootstrap5.dll": {}}}, "Blazorise.Charts/1.6.2": {"dependencies": {"Blazorise": "1.6.2", "Lambda2Js": "3.1.4", "Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Blazorise.Charts.dll": {"assemblyVersion": "1.6.2.0", "fileVersion": "1.6.2.0"}}, "compile": {"lib/net8.0/Blazorise.Charts.dll": {}}}, "Blazorise.Components/1.6.2": {"dependencies": {"Blazorise": "1.6.2", "Blazorise.Snackbar": "1.6.2", "Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Blazorise.Components.dll": {"assemblyVersion": "1.6.2.0", "fileVersion": "1.6.2.0"}}, "compile": {"lib/net8.0/Blazorise.Components.dll": {}}}, "Blazorise.DataGrid/1.6.2": {"dependencies": {"Blazorise": "1.6.2", "Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Blazorise.DataGrid.dll": {"assemblyVersion": "1.6.2.0", "fileVersion": "1.6.2.0"}}, "compile": {"lib/net8.0/Blazorise.DataGrid.dll": {}}}, "Blazorise.Icons.FontAwesome/1.6.2": {"dependencies": {"Blazorise": "1.6.2", "Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Blazorise.Icons.FontAwesome.dll": {"assemblyVersion": "1.6.2.0", "fileVersion": "1.6.2.0"}}, "compile": {"lib/net8.0/Blazorise.Icons.FontAwesome.dll": {}}}, "Blazorise.Licensing/1.2.0": {"runtime": {"lib/net8.0/Blazorise.Licensing.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}, "compile": {"lib/net8.0/Blazorise.Licensing.dll": {}}}, "Blazorise.Snackbar/1.6.2": {"dependencies": {"Blazorise": "1.6.2", "Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Blazorise.Snackbar.dll": {"assemblyVersion": "1.6.2.0", "fileVersion": "1.6.2.0"}}, "compile": {"lib/net8.0/Blazorise.Snackbar.dll": {}}}, "Bogus/35.6.1": {"runtime": {"lib/net6.0/Bogus.dll": {"assemblyVersion": "35.6.1.0", "fileVersion": "35.6.1.0"}}, "compile": {"lib/net6.0/Bogus.dll": {}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "5.1.1.0"}}, "compile": {"lib/net6.0/Castle.Core.dll": {}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}, "compile": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {}}}, "ClosedXML/0.104.2": {"dependencies": {"ClosedXML.Parser": "1.2.0", "DocumentFormat.OpenXml": "3.1.1", "ExcelNumberFormat": "1.1.0", "RBush": "4.0.0", "SixLabors.Fonts": "1.0.0"}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"assemblyVersion": "0.104.2.0", "fileVersion": "0.104.2.0"}}, "compile": {"lib/netstandard2.1/ClosedXML.dll": {}}}, "ClosedXML.Parser/1.2.0": {"runtime": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/ClosedXML.Parser.dll": {}}}, "DeviceDetector.NET/6.3.3": {"dependencies": {"LiteDB": "5.0.19", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Newtonsoft.Json": "13.0.3", "YamlDotNet": "15.1.2"}, "runtime": {"lib/net8.0/DeviceDetector.NET.dll": {"assemblyVersion": "6.3.3.0", "fileVersion": "6.3.3.0"}}, "compile": {"lib/net8.0/DeviceDetector.NET.dll": {}}}, "DocumentFormat.OpenXml/3.1.1": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.1.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.dll": {}}}, "DocumentFormat.OpenXml.Framework/3.1.1": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}, "compile": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {}}}, "ExcelNumberFormat/1.1.0": {"runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}, "compile": {"lib/netstandard2.0/ExcelNumberFormat.dll": {}}}, "Grpc.Core.Api/2.35.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "2.35.0.0"}}, "compile": {"lib/netstandard2.0/Grpc.Core.Api.dll": {}}}, "Grpc.Net.Client/2.34.0": {"dependencies": {"Grpc.Net.Common": "2.34.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net5.0/Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "2.34.0.0"}}, "compile": {"lib/net5.0/Grpc.Net.Client.dll": {}}}, "Grpc.Net.Common/2.34.0": {"dependencies": {"Grpc.Core.Api": "2.35.0"}, "runtime": {"lib/net5.0/Grpc.Net.Common.dll": {"assemblyVersion": "*******", "fileVersion": "2.34.0.0"}}, "compile": {"lib/net5.0/Grpc.Net.Common.dll": {}}}, "Humanizer.Core/3.0.0-beta.54": {"dependencies": {"System.Memory": "4.5.5", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net8.0/Humanizer.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}, "compile": {"lib/net8.0/Humanizer.dll": {}}}, "IdentityModel/7.0.0": {"runtime": {"lib/net6.0/IdentityModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/IdentityModel.dll": {}}}, "JetBrains.Annotations/2024.2.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2024.2.0.0"}}, "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {}}}, "Lambda2Js/3.1.4": {"runtime": {"lib/netstandard2.0/Lambda2Js.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Lambda2Js.dll": {}}}, "LiteDB/5.0.19": {"runtime": {"lib/netstandard2.0/LiteDB.dll": {"assemblyVersion": "5.0.19.0", "fileVersion": "5.0.19.0"}}, "compile": {"lib/netstandard2.0/LiteDB.dll": {}}}, "MagicOnion.Abstractions/5.1.8": {"dependencies": {"Grpc.Core.Api": "2.35.0", "MessagePack": "2.2.85"}, "runtime": {"lib/netstandard2.0/MagicOnion.Abstractions.dll": {"assemblyVersion": "5.1.8.0", "fileVersion": "5.1.8.0"}}, "compile": {"lib/netstandard2.0/MagicOnion.Abstractions.dll": {}}}, "MagicOnion.Client/5.1.8": {"dependencies": {"Grpc.Net.Client": "2.34.0", "MagicOnion.Abstractions": "5.1.8", "MagicOnion.Shared": "5.1.8"}, "runtime": {"lib/net7.0/MagicOnion.Client.dll": {"assemblyVersion": "5.1.8.0", "fileVersion": "5.1.8.0"}}, "compile": {"lib/net7.0/MagicOnion.Client.dll": {}}}, "MagicOnion.Shared/5.1.8": {"dependencies": {"Grpc.Core.Api": "2.35.0", "MagicOnion.Abstractions": "5.1.8"}, "runtime": {"lib/netstandard2.0/MagicOnion.Shared.dll": {"assemblyVersion": "5.1.8.0", "fileVersion": "5.1.8.0"}}, "compile": {"lib/netstandard2.0/MagicOnion.Shared.dll": {}}}, "MessagePack/2.2.85": {"dependencies": {"MessagePack.Annotations": "2.2.85", "Microsoft.Bcl.AsyncInterfaces": "9.0.0", "System.Collections.Immutable": "9.0.0", "System.Memory": "4.5.5", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.Lightweight": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.3"}, "runtime": {"lib/netcoreapp2.1/MessagePack.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.85.21899"}}, "compile": {"lib/netcoreapp2.1/MessagePack.dll": {}}}, "MessagePack.Annotations/2.2.85": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.85.21899"}}, "compile": {"lib/netstandard2.0/MessagePack.Annotations.dll": {}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/9.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {}}}, "Microsoft.AspNetCore.Authorization/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.AspNetCore.Components/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Analyzers": "9.0.0"}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.0": {}, "Microsoft.AspNetCore.Components.Authorization/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.0", "Microsoft.AspNetCore.Components": "9.0.0"}}, "Microsoft.AspNetCore.Components.Forms/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.0"}}, "Microsoft.AspNetCore.Components.Web/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.0", "Microsoft.AspNetCore.Components.Forms": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0", "Microsoft.JSInterop": "9.0.0"}}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.JSInterop.WebAssembly": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {}}}, "Microsoft.AspNetCore.Components.WebAssembly.Authentication/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Authentication.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Authentication.dll": {}}}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer/9.0.0": {}, "Microsoft.AspNetCore.Components.WebAssembly.Server/9.0.0": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Server.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.0": {}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.0"}}, "Microsoft.AspNetCore.Metadata/9.0.0": {}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {}}}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/9.0.0": {"dependencies": {"Microsoft.Net.Http.Headers": "9.0.0"}}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.123.58001"}}, "compile": {"lib/net8.0/Microsoft.Bcl.TimeProvider.dll": {}}}, "Microsoft.Build.Framework/17.8.3": {}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "9.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "3.0.0-beta.54", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "3.0.0-beta.54", "Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.EntityFrameworkCore/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.0": {}, "Microsoft.EntityFrameworkCore.Design/9.0.0": {"dependencies": {"Humanizer.Core": "3.0.0-beta.54", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52902"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.0"}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Caching.Hybrid/9.0.0-preview.7.24406.2": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Hybrid.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.40602"}}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Hybrid.dll": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0"}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.Diagnostics/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.FileProviders.Composite/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}}, "Microsoft.Extensions.Http/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Identity.Core/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Localization/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Localization.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Localization.Abstractions/9.0.0": {}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.Extensions.Primitives/9.0.0": {}, "Microsoft.IdentityModel.Abstractions/8.1.0": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.50924"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/8.1.0": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.1.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.50924"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/8.1.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.1.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.50924"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.1.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.1.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/8.1.0": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Logging": "8.1.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.50924"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.JSInterop/9.0.0": {}, "Microsoft.JSInterop.WebAssembly/9.0.0": {"dependencies": {"Microsoft.JSInterop": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {}}}, "Microsoft.Net.Http.Headers/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.6.14": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.14.0", "fileVersion": "1.6.14.0"}}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.1"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}, "compile": {"lib/net6.0/Newtonsoft.Json.dll": {}}}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "9.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {}}}, "Npgsql/9.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Npgsql.dll": {}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.0", "Npgsql": "9.0.0"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {}}}, "NUglify/1.21.9": {"runtime": {"lib/net5.0/NUglify.dll": {"assemblyVersion": "1.21.9.0", "fileVersion": "1.21.9.0"}}, "compile": {"lib/net5.0/NUglify.dll": {}}}, "OpenIddict.Abstractions/5.8.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0", "Microsoft.IdentityModel.Tokens": "8.1.0"}, "runtime": {"lib/net8.0/OpenIddict.Abstractions.dll": {"assemblyVersion": "5.8.0.0", "fileVersion": "5.800.24.42237"}}, "compile": {"lib/net8.0/OpenIddict.Abstractions.dll": {}}}, "OpenIddict.Core/5.8.0": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "OpenIddict.Abstractions": "5.8.0"}, "runtime": {"lib/net8.0/OpenIddict.Core.dll": {"assemblyVersion": "5.8.0.0", "fileVersion": "5.800.24.42237"}}, "compile": {"lib/net8.0/OpenIddict.Core.dll": {}}}, "OpenIddict.Server/5.8.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Microsoft.IdentityModel.JsonWebTokens": "8.1.0", "OpenIddict.Abstractions": "5.8.0"}, "runtime": {"lib/net8.0/OpenIddict.Server.dll": {"assemblyVersion": "5.8.0.0", "fileVersion": "5.800.24.42237"}}, "compile": {"lib/net8.0/OpenIddict.Server.dll": {}}}, "OpenIddict.Server.AspNetCore/5.8.0": {"dependencies": {"OpenIddict.Server": "5.8.0"}, "runtime": {"lib/net8.0/OpenIddict.Server.AspNetCore.dll": {"assemblyVersion": "5.8.0.0", "fileVersion": "5.800.24.42237"}}, "compile": {"lib/net8.0/OpenIddict.Server.AspNetCore.dll": {}}}, "OpenIddict.Validation/5.8.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Microsoft.IdentityModel.JsonWebTokens": "8.1.0", "Microsoft.IdentityModel.Protocols": "8.0.1", "OpenIddict.Abstractions": "5.8.0"}, "runtime": {"lib/net8.0/OpenIddict.Validation.dll": {"assemblyVersion": "5.8.0.0", "fileVersion": "5.800.24.42237"}}, "compile": {"lib/net8.0/OpenIddict.Validation.dll": {}}}, "OpenIddict.Validation.AspNetCore/5.8.0": {"dependencies": {"OpenIddict.Validation": "5.8.0"}, "runtime": {"lib/net8.0/OpenIddict.Validation.AspNetCore.dll": {"assemblyVersion": "5.8.0.0", "fileVersion": "5.800.24.42237"}}, "compile": {"lib/net8.0/OpenIddict.Validation.AspNetCore.dll": {}}}, "OpenIddict.Validation.ServerIntegration/5.8.0": {"dependencies": {"OpenIddict.Server": "5.8.0", "OpenIddict.Validation": "5.8.0"}, "runtime": {"lib/net8.0/OpenIddict.Validation.ServerIntegration.dll": {"assemblyVersion": "5.8.0.0", "fileVersion": "5.800.24.42237"}}, "compile": {"lib/net8.0/OpenIddict.Validation.ServerIntegration.dll": {}}}, "Polly/8.4.2": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net6.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}, "compile": {"lib/net6.0/Polly.dll": {}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}, "compile": {"lib/net8.0/Polly.Core.dll": {}}}, "RBush/4.0.0": {"runtime": {"lib/net8.0/RBush.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/RBush.dll": {}}}, "Scriban/5.10.0": {"runtime": {"lib/net7.0/Scriban.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}, "compile": {"lib/net7.0/Scriban.dll": {}}}, "Serilog/4.0.2": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.dll": {}}}, "Serilog.AspNetCore/8.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Serilog": "4.0.2", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.2", "Serilog.Sinks.Console": "5.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net8.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.AspNetCore.dll": {}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Serilog": "4.0.2", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Serilog": "4.0.2"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Extensions.Logging.dll": {}}}, "Serilog.Formatting.Compact/2.0.0": {"dependencies": {"Serilog": "4.0.2"}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/Serilog.Formatting.Compact.dll": {}}}, "Serilog.Settings.Configuration/8.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.0.2"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Settings.Configuration.dll": {}}}, "Serilog.Sinks.Async/2.0.0": {"dependencies": {"Serilog": "4.0.2"}, "runtime": {"lib/net8.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Sinks.Async.dll": {}}}, "Serilog.Sinks.Console/5.0.0": {"dependencies": {"Serilog": "4.0.2"}, "runtime": {"lib/net7.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/Serilog.Sinks.Console.dll": {}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "4.0.2"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "4.0.2"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Serilog.Sinks.File.dll": {}}}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {}}}, "Swashbuckle.AspNetCore/6.8.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.8.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.8.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.8.1"}}, "Swashbuckle.AspNetCore.Swagger/6.8.1": {"dependencies": {"Microsoft.OpenApi": "1.6.14"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.8.1"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.1": {"runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.1.756"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/9.0.0": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Diagnostics.EventLog/6.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/8.1.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.1.0", "Microsoft.IdentityModel.Tokens": "8.1.0"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.0.50924"}}, "compile": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "compile": {"lib/net8.0/System.IO.Packaging.dll": {}}}, "System.IO.Pipelines/7.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.4.5": {"runtime": {"lib/net8.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/System.Linq.Dynamic.Core.dll": {}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.6.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.6.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.6.0": {}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/7.0.0": {"dependencies": {"System.Collections.Immutable": "9.0.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/9.0.0": {}, "System.Text.Json/9.0.0": {}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels/7.0.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.3": {}, "System.ValueTuple/4.5.0": {}, "TimeZoneConverter/6.1.0": {"runtime": {"lib/net6.0/TimeZoneConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/TimeZoneConverter.dll": {}}}, "Volo.Abp.Account.Application/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "System.Text.Encodings.Web": "9.0.0", "Volo.Abp.Account.Application.Contracts": "9.0.1", "Volo.Abp.Emailing": "9.0.1", "Volo.Abp.Identity.Application": "9.0.1", "Volo.Abp.UI.Navigation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Account.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Account.Application.dll": {}}}, "Volo.Abp.Account.Application.Contracts/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Identity.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Account.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Account.Application.Contracts.dll": {}}}, "Volo.Abp.Account.HttpApi/9.0.1": {"dependencies": {"Volo.Abp.Account.Application.Contracts": "9.0.1", "Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.Identity.HttpApi": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Account.HttpApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Account.HttpApi.dll": {}}}, "Volo.Abp.Account.HttpApi.Client/9.0.1": {"dependencies": {"Volo.Abp.Account.Application.Contracts": "9.0.1", "Volo.Abp.Http.Client": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Account.HttpApi.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Account.HttpApi.Client.dll": {}}}, "Volo.Abp.Account.Web/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Account.Application.Contracts": "9.0.1", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": "9.0.1", "Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.Identity.AspNetCore": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Account.Web.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Account.Web.dll": {}}}, "Volo.Abp.Account.Web.OpenIddict/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "System.Security.Principal.Windows": "5.0.0", "Volo.Abp.Account.Web": "9.0.1", "Volo.Abp.OpenIddict.AspNetCore": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Account.Web.OpenIddict.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Account.Web.OpenIddict.dll": {}}}, "Volo.Abp.ApiVersioning.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.ApiVersioning.Abstractions.dll": {}}}, "Volo.Abp.AspNetCore/9.0.1": {"dependencies": {"DeviceDetector.NET": "6.3.3", "IdentityModel": "7.0.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "9.0.0", "Volo.Abp.AspNetCore.Abstractions": "9.0.1", "Volo.Abp.Auditing": "9.0.1", "Volo.Abp.Authorization": "9.0.1", "Volo.Abp.ExceptionHandling": "9.0.1", "Volo.Abp.Http": "9.0.1", "Volo.Abp.Security": "9.0.1", "Volo.Abp.Uow": "9.0.1", "Volo.Abp.Validation": "9.0.1", "Volo.Abp.VirtualFileSystem": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.dll": {}}}, "Volo.Abp.AspNetCore.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Abstractions.dll": {}}}, "Volo.Abp.AspNetCore.Components/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.0", "Volo.Abp.MultiTenancy.Abstractions": "9.0.1", "Volo.Abp.ObjectMapping": "9.0.1", "Volo.Abp.Security": "9.0.1", "Volo.Abp.Timing": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Components.dll": {}}}, "Volo.Abp.AspNetCore.Components.Web/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.0", "Volo.Abp.AspNetCore.Components": "9.0.1", "Volo.Abp.UI": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Components.Web.dll": {}}}, "Volo.Abp.AspNetCore.Components.Web.LeptonXLiteTheme/4.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.Web.Theming": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Components.Web.LeptonXLiteTheme.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.0.1.0"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Components.Web.LeptonXLiteTheme.dll": {}}}, "Volo.Abp.AspNetCore.Components.Web.Theming/9.0.1": {"dependencies": {"Volo.Abp.BlazoriseUI": "9.0.1", "Volo.Abp.UI.Navigation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Components.Web.Theming.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Components.Web.Theming.dll": {}}}, "Volo.Abp.AspNetCore.Components.WebAssembly/9.0.1": {"dependencies": {"IdentityModel": "7.0.0", "Microsoft.AspNetCore.Components.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.WebAssembly": "9.0.0", "Microsoft.AspNetCore.Components.WebAssembly.Authentication": "9.0.0", "Microsoft.AspNetCore.WebUtilities": "9.0.0", "System.IdentityModel.Tokens.Jwt": "8.1.0", "Volo.Abp.AspNetCore.Components.Web": "9.0.1", "Volo.Abp.AspNetCore.Mvc.Client.Common": "9.0.1", "Volo.Abp.UI": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Components.WebAssembly.dll": {}}}, "Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXLiteTheme/4.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.Web.LeptonXLiteTheme": "4.0.1", "Volo.Abp.AspNetCore.Components.WebAssembly.Theming": "9.0.1", "Volo.Abp.Http.Client.IdentityModel.WebAssembly": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXLiteTheme.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.0.1.0"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXLiteTheme.dll": {}}}, "Volo.Abp.AspNetCore.Components.WebAssembly.Theming/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.Web.Theming": "9.0.1", "Volo.Abp.AspNetCore.Components.WebAssembly": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Components.WebAssembly.Theming.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Components.WebAssembly.Theming.dll": {}}}, "Volo.Abp.AspNetCore.MultiTenancy/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.MultiTenancy.dll": {}}}, "Volo.Abp.AspNetCore.Mvc/9.0.1": {"dependencies": {"Asp.Versioning.Mvc": "8.1.0", "Asp.Versioning.Mvc.ApiExplorer": "8.1.0", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "9.0.0", "Volo.Abp.ApiVersioning.Abstractions": "9.0.1", "Volo.Abp.AspNetCore": "9.0.1", "Volo.Abp.AspNetCore.Mvc.Contracts": "9.0.1", "Volo.Abp.Ddd.Application": "9.0.1", "Volo.Abp.GlobalFeatures": "9.0.1", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.UI.Navigation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.Client.Common/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.Contracts": "9.0.1", "Volo.Abp.Authorization": "9.0.1", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.Features": "9.0.1", "Volo.Abp.Http.Client": "9.0.1", "Volo.Abp.Localization": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.Client.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.Client.Common.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.Contracts/9.0.1": {"dependencies": {"Volo.Abp.Ddd.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI/9.0.1": {"dependencies": {"NUglify": "1.21.9", "Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.UI.Navigation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "9.0.1", "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "9.0.1", "Volo.Abp.Minify": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.MultiTenancy": "9.0.1", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions": "9.0.1", "Volo.Abp.Localization": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Packages.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite/4.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy": "9.0.1", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": "9.0.1", "Volo.Abp.AutoMapper": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.0.1.0"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bootstrap": "9.0.1", "Volo.Abp.AspNetCore.Mvc.UI.Packages": "9.0.1", "Volo.Abp.AspNetCore.Mvc.UI.Widgets": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.dll": {}}}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc.UI.Bundling": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Mvc.UI.Widgets.dll": {}}}, "Volo.Abp.AspNetCore.Serilog/9.0.1": {"dependencies": {"Serilog": "4.0.2", "Volo.Abp.AspNetCore": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AspNetCore.Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AspNetCore.Serilog.dll": {}}}, "Volo.Abp.Auditing/9.0.1": {"dependencies": {"Volo.Abp.Auditing.Contracts": "9.0.1", "Volo.Abp.Data": "9.0.1", "Volo.Abp.Json": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.Security": "9.0.1", "Volo.Abp.Threading": "9.0.1", "Volo.Abp.Timing": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Auditing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Auditing.dll": {}}}, "Volo.Abp.Auditing.Contracts/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Auditing.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Auditing.Contracts.dll": {}}}, "Volo.Abp.AuditLogging.Domain/9.0.1": {"dependencies": {"Volo.Abp.AuditLogging.Domain.Shared": "9.0.1", "Volo.Abp.Auditing": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AuditLogging.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AuditLogging.Domain.dll": {}}}, "Volo.Abp.AuditLogging.Domain.Shared/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AuditLogging.Domain.Shared.dll": {}}}, "Volo.Abp.AuditLogging.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.AuditLogging.Domain": "9.0.1", "Volo.Abp.EntityFrameworkCore": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AuditLogging.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AuditLogging.EntityFrameworkCore.dll": {}}}, "Volo.Abp.Authorization/9.0.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.0.1", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.Security": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Authorization.dll": {}}}, "Volo.Abp.Authorization.Abstractions/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.0", "Volo.Abp.MultiTenancy.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Authorization.Abstractions.dll": {}}}, "Volo.Abp.Autofac/9.0.1": {"dependencies": {"Autofac": "8.1.0", "Autofac.Extensions.DependencyInjection": "10.0.0", "Autofac.Extras.DynamicProxy": "7.1.0", "Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Volo.Abp.Castle.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Autofac.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Autofac.dll": {}}}, "Volo.Abp.Autofac.WebAssembly/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.WebAssembly": "9.0.1", "Volo.Abp.Autofac": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Autofac.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Autofac.WebAssembly.dll": {}}}, "Volo.Abp.AutoMapper/9.0.1": {"dependencies": {"AutoMapper": "13.0.1", "Volo.Abp.Auditing": "9.0.1", "Volo.Abp.ObjectExtending": "9.0.1", "Volo.Abp.ObjectMapping": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.AutoMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.AutoMapper.dll": {}}}, "Volo.Abp.BackgroundJobs/9.0.1": {"dependencies": {"Volo.Abp.BackgroundJobs.Abstractions": "9.0.1", "Volo.Abp.BackgroundWorkers": "9.0.1", "Volo.Abp.DistributedLocking.Abstractions": "9.0.1", "Volo.Abp.Guids": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.Timing": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BackgroundJobs.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BackgroundJobs.dll": {}}}, "Volo.Abp.BackgroundJobs.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Json": "9.0.1", "Volo.Abp.MultiTenancy.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BackgroundJobs.Abstractions.dll": {}}}, "Volo.Abp.BackgroundJobs.Domain/9.0.1": {"dependencies": {"Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.BackgroundJobs": "9.0.1", "Volo.Abp.BackgroundJobs.Domain.Shared": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BackgroundJobs.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BackgroundJobs.Domain.dll": {}}}, "Volo.Abp.BackgroundJobs.Domain.Shared/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BackgroundJobs.Domain.Shared.dll": {}}}, "Volo.Abp.BackgroundJobs.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.BackgroundJobs.Domain": "9.0.1", "Volo.Abp.EntityFrameworkCore": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BackgroundJobs.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BackgroundJobs.EntityFrameworkCore.dll": {}}}, "Volo.Abp.BackgroundWorkers/9.0.1": {"dependencies": {"Volo.Abp.Threading": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BackgroundWorkers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BackgroundWorkers.dll": {}}}, "Volo.Abp.BlazoriseUI/9.0.1": {"dependencies": {"Blazorise": "1.6.2", "Blazorise.Components": "1.6.2", "Blazorise.DataGrid": "1.6.2", "Blazorise.Snackbar": "1.6.2", "Volo.Abp.AspNetCore.Components.Web": "9.0.1", "Volo.Abp.Authorization": "9.0.1", "Volo.Abp.Ddd.Application.Contracts": "9.0.1", "Volo.Abp.Features": "9.0.1", "Volo.Abp.GlobalFeatures": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BlazoriseUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BlazoriseUI.dll": {}}}, "Volo.Abp.BlobStoring/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.Threading": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BlobStoring.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BlobStoring.dll": {}}}, "Volo.Abp.BlobStoring.Database.Domain/9.0.1": {"dependencies": {"Volo.Abp.BlobStoring": "9.0.1", "Volo.Abp.BlobStoring.Database.Domain.Shared": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BlobStoring.Database.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BlobStoring.Database.Domain.dll": {}}}, "Volo.Abp.BlobStoring.Database.Domain.Shared/9.0.1": {"dependencies": {"Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BlobStoring.Database.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BlobStoring.Database.Domain.Shared.dll": {}}}, "Volo.Abp.BlobStoring.Database.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.BlobStoring.Database.Domain": "9.0.1", "Volo.Abp.EntityFrameworkCore": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.BlobStoring.Database.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.BlobStoring.Database.EntityFrameworkCore.dll": {}}}, "Volo.Abp.Caching/9.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Hybrid": "9.0.0-preview.7.24406.2", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Volo.Abp.Json": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.Serialization": "9.0.1", "Volo.Abp.Threading": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Caching.dll": {}}}, "Volo.Abp.Castle.Core/9.0.1": {"dependencies": {"Castle.Core": "5.1.1", "Castle.Core.AsyncInterceptor": "2.1.0", "Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Castle.Core.dll": {}}}, "Volo.Abp.Core/9.0.1": {"dependencies": {"JetBrains.Annotations": "2024.2.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Localization": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "9.0.0", "System.Linq.Dynamic.Core": "1.4.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "9.0.0"}, "runtime": {"lib/net9.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Core.dll": {}}}, "Volo.Abp.Data/9.0.1": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "9.0.1", "Volo.Abp.ObjectExtending": "9.0.1", "Volo.Abp.Uow": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Data.dll": {}}}, "Volo.Abp.Ddd.Application/9.0.1": {"dependencies": {"Volo.Abp.Authorization": "9.0.1", "Volo.Abp.Ddd.Application.Contracts": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.Features": "9.0.1", "Volo.Abp.GlobalFeatures": "9.0.1", "Volo.Abp.Http.Abstractions": "9.0.1", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.ObjectMapping": "9.0.1", "Volo.Abp.Security": "9.0.1", "Volo.Abp.Settings": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Ddd.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Ddd.Application.dll": {}}}, "Volo.Abp.Ddd.Application.Contracts/9.0.1": {"dependencies": {"Volo.Abp.Auditing.Contracts": "9.0.1", "Volo.Abp.Data": "9.0.1", "Volo.Abp.Localization": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Ddd.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Ddd.Application.Contracts.dll": {}}}, "Volo.Abp.Ddd.Domain/9.0.1": {"dependencies": {"Volo.Abp.Auditing": "9.0.1", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.Data": "9.0.1", "Volo.Abp.Ddd.Domain.Shared": "9.0.1", "Volo.Abp.EventBus": "9.0.1", "Volo.Abp.ExceptionHandling": "9.0.1", "Volo.Abp.Guids": "9.0.1", "Volo.Abp.ObjectMapping": "9.0.1", "Volo.Abp.Specifications": "9.0.1", "Volo.Abp.Timing": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Ddd.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Ddd.Domain.dll": {}}}, "Volo.Abp.Ddd.Domain.Shared/9.0.1": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "9.0.1", "Volo.Abp.MultiTenancy.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Ddd.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Ddd.Domain.Shared.dll": {}}}, "Volo.Abp.DistributedLocking.Abstractions/9.0.1": {"dependencies": {"AsyncKeyedLock": "7.0.1", "Microsoft.Bcl.AsyncInterfaces": "9.0.0", "Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.DistributedLocking.Abstractions.dll": {}}}, "Volo.Abp.Emailing/9.0.1": {"dependencies": {"Volo.Abp.BackgroundJobs.Abstractions": "9.0.1", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.Settings": "9.0.1", "Volo.Abp.TextTemplating": "9.0.1", "Volo.Abp.VirtualFileSystem": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Emailing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Emailing.dll": {}}}, "Volo.Abp.EntityFrameworkCore/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.0", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.Json": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.EntityFrameworkCore.dll": {}}}, "Volo.Abp.EntityFrameworkCore.PostgreSql/9.0.1": {"dependencies": {"Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.0", "Volo.Abp.EntityFrameworkCore": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.EntityFrameworkCore.PostgreSql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.EntityFrameworkCore.PostgreSql.dll": {}}}, "Volo.Abp.EventBus/9.0.1": {"dependencies": {"Volo.Abp.BackgroundWorkers": "9.0.1", "Volo.Abp.DistributedLocking.Abstractions": "9.0.1", "Volo.Abp.EventBus.Abstractions": "9.0.1", "Volo.Abp.Guids": "9.0.1", "Volo.Abp.Json": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.EventBus.dll": {}}}, "Volo.Abp.EventBus.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.ObjectExtending": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.EventBus.Abstractions.dll": {}}}, "Volo.Abp.ExceptionHandling/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Data": "9.0.1", "Volo.Abp.Localization": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.ExceptionHandling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.ExceptionHandling.dll": {}}}, "Volo.Abp.FeatureManagement.Application/9.0.1": {"dependencies": {"Volo.Abp.Ddd.Application": "9.0.1", "Volo.Abp.FeatureManagement.Application.Contracts": "9.0.1", "Volo.Abp.FeatureManagement.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.Application.dll": {}}}, "Volo.Abp.FeatureManagement.Application.Contracts/9.0.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.0.1", "Volo.Abp.Ddd.Application.Contracts": "9.0.1", "Volo.Abp.FeatureManagement.Domain.Shared": "9.0.1", "Volo.Abp.Json": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.Application.Contracts.dll": {}}}, "Volo.Abp.FeatureManagement.Blazor/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.Web.Theming": "9.0.1", "Volo.Abp.FeatureManagement.Application.Contracts": "9.0.1", "Volo.Abp.Features": "9.0.1", "Volo.Abp.SettingManagement.Blazor": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.Blazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.Blazor.dll": {}}}, "Volo.Abp.FeatureManagement.Blazor.WebAssembly/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.WebAssembly.Theming": "9.0.1", "Volo.Abp.FeatureManagement.Blazor": "9.0.1", "Volo.Abp.FeatureManagement.HttpApi.Client": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.Blazor.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.Blazor.WebAssembly.dll": {}}}, "Volo.Abp.FeatureManagement.Domain/9.0.1": {"dependencies": {"Polly": "8.4.2", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.FeatureManagement.Domain.Shared": "9.0.1", "Volo.Abp.Features": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.Domain.dll": {}}}, "Volo.Abp.FeatureManagement.Domain.Shared/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Json.SystemTextJson": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.Domain.Shared.dll": {}}}, "Volo.Abp.FeatureManagement.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.EntityFrameworkCore": "9.0.1", "Volo.Abp.FeatureManagement.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.EntityFrameworkCore.dll": {}}}, "Volo.Abp.FeatureManagement.HttpApi/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.FeatureManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.HttpApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.HttpApi.dll": {}}}, "Volo.Abp.FeatureManagement.HttpApi.Client/9.0.1": {"dependencies": {"Volo.Abp.FeatureManagement.Application.Contracts": "9.0.1", "Volo.Abp.Http.Client": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.FeatureManagement.HttpApi.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.FeatureManagement.HttpApi.Client.dll": {}}}, "Volo.Abp.Features/9.0.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.0.1", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Features.dll": {}}}, "Volo.Abp.GlobalFeatures/9.0.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.0.1", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.VirtualFileSystem": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.GlobalFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.GlobalFeatures.dll": {}}}, "Volo.Abp.Guids/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Guids.dll": {}}}, "Volo.Abp.Http/9.0.1": {"dependencies": {"Volo.Abp.Http.Abstractions": "9.0.1", "Volo.Abp.Json": "9.0.1", "Volo.Abp.Minify": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Http.dll": {}}}, "Volo.Abp.Http.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Http.Abstractions.dll": {}}}, "Volo.Abp.Http.Client/9.0.1": {"dependencies": {"Microsoft.Extensions.Http": "9.0.0", "Volo.Abp.Castle.Core": "9.0.1", "Volo.Abp.EventBus": "9.0.1", "Volo.Abp.ExceptionHandling": "9.0.1", "Volo.Abp.Http": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.RemoteServices": "9.0.1", "Volo.Abp.Threading": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Http.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Http.Client.dll": {}}}, "Volo.Abp.Http.Client.IdentityModel/9.0.1": {"dependencies": {"Volo.Abp.Http.Client": "9.0.1", "Volo.Abp.IdentityModel": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Http.Client.IdentityModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Http.Client.IdentityModel.dll": {}}}, "Volo.Abp.Http.Client.IdentityModel.WebAssembly/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components.WebAssembly.Authentication": "9.0.0", "Volo.Abp.AspNetCore.Components.WebAssembly": "9.0.1", "Volo.Abp.Http.Client.IdentityModel": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Http.Client.IdentityModel.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Http.Client.IdentityModel.WebAssembly.dll": {}}}, "Volo.Abp.Identity.Application/9.0.1": {"dependencies": {"Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.Identity.Application.Contracts": "9.0.1", "Volo.Abp.Identity.Domain": "9.0.1", "Volo.Abp.PermissionManagement.Application": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.Application.dll": {}}}, "Volo.Abp.Identity.Application.Contracts/9.0.1": {"dependencies": {"Volo.Abp.Authorization": "9.0.1", "Volo.Abp.Identity.Domain.Shared": "9.0.1", "Volo.Abp.PermissionManagement.Application.Contracts": "9.0.1", "Volo.Abp.Users.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.Application.Contracts.dll": {}}}, "Volo.Abp.Identity.AspNetCore/9.0.1": {"dependencies": {"Volo.Abp.Identity.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.AspNetCore.dll": {}}}, "Volo.Abp.Identity.Blazor/9.0.1": {"dependencies": {"Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.Identity.Application.Contracts": "9.0.1", "Volo.Abp.PermissionManagement.Blazor": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.Blazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.Blazor.dll": {}}}, "Volo.Abp.Identity.Blazor.WebAssembly/9.0.1": {"dependencies": {"Volo.Abp.Identity.Blazor": "9.0.1", "Volo.Abp.Identity.HttpApi.Client": "9.0.1", "Volo.Abp.PermissionManagement.Blazor.WebAssembly": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.Blazor.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.Blazor.WebAssembly.dll": {}}}, "Volo.Abp.Identity.Domain/9.0.1": {"dependencies": {"Microsoft.Extensions.Identity.Core": "9.0.0", "Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.Identity.Domain.Shared": "9.0.1", "Volo.Abp.Users.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.Domain.dll": {}}}, "Volo.Abp.Identity.Domain.Shared/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Auditing.Contracts": "9.0.1", "Volo.Abp.Features": "9.0.1", "Volo.Abp.Users.Domain.Shared": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.Domain.Shared.dll": {}}}, "Volo.Abp.Identity.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.Identity.Domain": "9.0.1", "Volo.Abp.Users.EntityFrameworkCore": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.EntityFrameworkCore.dll": {}}}, "Volo.Abp.Identity.HttpApi/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.Identity.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.HttpApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.HttpApi.dll": {}}}, "Volo.Abp.Identity.HttpApi.Client/9.0.1": {"dependencies": {"Volo.Abp.Http.Client": "9.0.1", "Volo.Abp.Identity.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Identity.HttpApi.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Identity.HttpApi.Client.dll": {}}}, "Volo.Abp.IdentityModel/9.0.1": {"dependencies": {"IdentityModel": "7.0.0", "Microsoft.Extensions.Http": "9.0.0", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.Threading": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.IdentityModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.IdentityModel.dll": {}}}, "Volo.Abp.Json/9.0.1": {"dependencies": {"Volo.Abp.Json.SystemTextJson": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Json.dll": {}}}, "Volo.Abp.Json.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Json.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Json.Abstractions.dll": {}}}, "Volo.Abp.Json.SystemTextJson/9.0.1": {"dependencies": {"System.Text.Json": "9.0.0", "Volo.Abp.Data": "9.0.1", "Volo.Abp.Json.Abstractions": "9.0.1", "Volo.Abp.Timing": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Json.SystemTextJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Json.SystemTextJson.dll": {}}}, "Volo.Abp.Localization/9.0.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "9.0.1", "Volo.Abp.Settings": "9.0.1", "Volo.Abp.Threading": "9.0.1", "Volo.Abp.VirtualFileSystem": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Localization.dll": {}}}, "Volo.Abp.Localization.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Localization.Abstractions.dll": {}}}, "Volo.Abp.Minify/9.0.1": {"dependencies": {"NUglify": "1.21.9", "Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Minify.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Minify.dll": {}}}, "Volo.Abp.MultiTenancy/9.0.1": {"dependencies": {"Volo.Abp.Data": "9.0.1", "Volo.Abp.EventBus.Abstractions": "9.0.1", "Volo.Abp.MultiTenancy.Abstractions": "9.0.1", "Volo.Abp.Security": "9.0.1", "Volo.Abp.Settings": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.MultiTenancy.dll": {}}}, "Volo.Abp.MultiTenancy.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Localization": "9.0.1", "Volo.Abp.VirtualFileSystem": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.MultiTenancy.Abstractions.dll": {}}}, "Volo.Abp.ObjectExtending/9.0.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "9.0.1", "Volo.Abp.Validation.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.ObjectExtending.dll": {}}}, "Volo.Abp.ObjectMapping/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.ObjectMapping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.ObjectMapping.dll": {}}}, "Volo.Abp.OpenIddict.AspNetCore/9.0.1": {"dependencies": {"OpenIddict.Server.AspNetCore": "5.8.0", "OpenIddict.Validation.AspNetCore": "5.8.0", "OpenIddict.Validation.ServerIntegration": "5.8.0", "Volo.Abp.AspNetCore.MultiTenancy": "9.0.1", "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared": "9.0.1", "Volo.Abp.OpenIddict.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.OpenIddict.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.OpenIddict.AspNetCore.dll": {}}}, "Volo.Abp.OpenIddict.Domain/9.0.1": {"dependencies": {"OpenIddict.Core": "5.8.0", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.DistributedLocking.Abstractions": "9.0.1", "Volo.Abp.Identity.Domain": "9.0.1", "Volo.Abp.OpenIddict.Domain.Shared": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.OpenIddict.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.OpenIddict.Domain.dll": {}}}, "Volo.Abp.OpenIddict.Domain.Shared/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "OpenIddict.Abstractions": "5.8.0", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.OpenIddict.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.OpenIddict.Domain.Shared.dll": {}}}, "Volo.Abp.OpenIddict.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.EntityFrameworkCore": "9.0.1", "Volo.Abp.OpenIddict.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.OpenIddict.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.OpenIddict.EntityFrameworkCore.dll": {}}}, "Volo.Abp.PermissionManagement.Application/9.0.1": {"dependencies": {"Volo.Abp.Ddd.Application": "9.0.1", "Volo.Abp.PermissionManagement.Application.Contracts": "9.0.1", "Volo.Abp.PermissionManagement.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.Application.dll": {}}}, "Volo.Abp.PermissionManagement.Application.Contracts/9.0.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.0.1", "Volo.Abp.Ddd.Application.Contracts": "9.0.1", "Volo.Abp.PermissionManagement.Domain.Shared": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.Application.Contracts.dll": {}}}, "Volo.Abp.PermissionManagement.Blazor/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.Web.Theming": "9.0.1", "Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.PermissionManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.Blazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.Blazor.dll": {}}}, "Volo.Abp.PermissionManagement.Blazor.WebAssembly/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.WebAssembly.Theming": "9.0.1", "Volo.Abp.PermissionManagement.Blazor": "9.0.1", "Volo.Abp.PermissionManagement.HttpApi.Client": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.Blazor.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.Blazor.WebAssembly.dll": {}}}, "Volo.Abp.PermissionManagement.Domain/9.0.1": {"dependencies": {"Polly": "8.4.2", "Volo.Abp.Authorization": "9.0.1", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.Json": "9.0.1", "Volo.Abp.PermissionManagement.Domain.Shared": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.Domain.dll": {}}}, "Volo.Abp.PermissionManagement.Domain.Identity/9.0.1": {"dependencies": {"Volo.Abp.Identity.Domain.Shared": "9.0.1", "Volo.Abp.PermissionManagement.Domain": "9.0.1", "Volo.Abp.Users.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.Domain.Identity.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.Domain.Identity.dll": {}}}, "Volo.Abp.PermissionManagement.Domain.OpenIddict/9.0.1": {"dependencies": {"Volo.Abp.OpenIddict.Domain.Shared": "9.0.1", "Volo.Abp.PermissionManagement.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.Domain.OpenIddict.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.Domain.OpenIddict.dll": {}}}, "Volo.Abp.PermissionManagement.Domain.Shared/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.Domain.Shared.dll": {}}}, "Volo.Abp.PermissionManagement.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.EntityFrameworkCore": "9.0.1", "Volo.Abp.PermissionManagement.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.EntityFrameworkCore.dll": {}}}, "Volo.Abp.PermissionManagement.HttpApi/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.PermissionManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.HttpApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.HttpApi.dll": {}}}, "Volo.Abp.PermissionManagement.HttpApi.Client/9.0.1": {"dependencies": {"Volo.Abp.Http.Client": "9.0.1", "Volo.Abp.PermissionManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.PermissionManagement.HttpApi.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.PermissionManagement.HttpApi.Client.dll": {}}}, "Volo.Abp.RemoteServices/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1", "Volo.Abp.MultiTenancy.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.RemoteServices.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.RemoteServices.dll": {}}}, "Volo.Abp.Security/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Security.dll": {}}}, "Volo.Abp.Serialization/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Serialization.dll": {}}}, "Volo.Abp.SettingManagement.Application/9.0.1": {"dependencies": {"Volo.Abp.Ddd.Application": "9.0.1", "Volo.Abp.Emailing": "9.0.1", "Volo.Abp.SettingManagement.Application.Contracts": "9.0.1", "Volo.Abp.SettingManagement.Domain": "9.0.1", "Volo.Abp.Users.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.Application.dll": {}}}, "Volo.Abp.SettingManagement.Application.Contracts/9.0.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.0.1", "Volo.Abp.Ddd.Application.Contracts": "9.0.1", "Volo.Abp.SettingManagement.Domain.Shared": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.Application.Contracts.dll": {}}}, "Volo.Abp.SettingManagement.Blazor/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.Web.Theming": "9.0.1", "Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.SettingManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.Blazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.Blazor.dll": {}}}, "Volo.Abp.SettingManagement.Blazor.WebAssembly/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Components.WebAssembly.Theming": "9.0.1", "Volo.Abp.SettingManagement.Blazor": "9.0.1", "Volo.Abp.SettingManagement.HttpApi.Client": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.Blazor.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.Blazor.WebAssembly.dll": {}}}, "Volo.Abp.SettingManagement.Domain/9.0.1": {"dependencies": {"Polly": "8.4.2", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.SettingManagement.Domain.Shared": "9.0.1", "Volo.Abp.Settings": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.Domain.dll": {}}}, "Volo.Abp.SettingManagement.Domain.Shared/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Features": "9.0.1", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.Domain.Shared.dll": {}}}, "Volo.Abp.SettingManagement.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.EntityFrameworkCore": "9.0.1", "Volo.Abp.SettingManagement.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.EntityFrameworkCore.dll": {}}}, "Volo.Abp.SettingManagement.HttpApi/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.SettingManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.HttpApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.HttpApi.dll": {}}}, "Volo.Abp.SettingManagement.HttpApi.Client/9.0.1": {"dependencies": {"Volo.Abp.Http.Client": "9.0.1", "Volo.Abp.SettingManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.SettingManagement.HttpApi.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.SettingManagement.HttpApi.Client.dll": {}}}, "Volo.Abp.Settings/9.0.1": {"dependencies": {"Volo.Abp.Data": "9.0.1", "Volo.Abp.Localization.Abstractions": "9.0.1", "Volo.Abp.Security": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Settings.dll": {}}}, "Volo.Abp.Specifications/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Specifications.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Specifications.dll": {}}}, "Volo.Abp.Studio.Client/0.9.15": {"dependencies": {"MagicOnion.Client": "5.1.8", "Serilog": "4.0.2", "Volo.Abp.Studio.Client.Contracts": "0.9.15", "Volo.Abp.Threading": "9.0.1"}, "runtime": {"lib/net8.0/Volo.Abp.Studio.Client.dll": {"assemblyVersion": "0.9.15.0", "fileVersion": "0.9.15.0"}}, "compile": {"lib/net8.0/Volo.Abp.Studio.Client.dll": {}}}, "Volo.Abp.Studio.Client.AspNetCore/0.9.15": {"dependencies": {"Volo.Abp.AspNetCore": "9.0.1", "Volo.Abp.Studio.Client": "0.9.15"}, "runtime": {"lib/net8.0/Volo.Abp.Studio.Client.AspNetCore.dll": {"assemblyVersion": "0.9.15.0", "fileVersion": "0.9.15.0"}}, "compile": {"lib/net8.0/Volo.Abp.Studio.Client.AspNetCore.dll": {}}}, "Volo.Abp.Studio.Client.Contracts/0.9.15": {"dependencies": {"MagicOnion.Abstractions": "5.1.8", "Volo.Abp.Core": "9.0.1", "Volo.Abp.EventBus.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Volo.Abp.Studio.Client.Contracts.dll": {"assemblyVersion": "0.9.15.0", "fileVersion": "0.9.15.0"}}, "compile": {"lib/net8.0/Volo.Abp.Studio.Client.Contracts.dll": {}}}, "Volo.Abp.Swashbuckle/9.0.1": {"dependencies": {"Swashbuckle.AspNetCore": "6.8.1", "Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.VirtualFileSystem": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Swashbuckle.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Swashbuckle.dll": {}}}, "Volo.Abp.TenantManagement.Application/9.0.1": {"dependencies": {"Volo.Abp.Ddd.Application": "9.0.1", "Volo.Abp.TenantManagement.Application.Contracts": "9.0.1", "Volo.Abp.TenantManagement.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.Application.dll": {}}}, "Volo.Abp.TenantManagement.Application.Contracts/9.0.1": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "9.0.1", "Volo.Abp.Ddd.Application.Contracts": "9.0.1", "Volo.Abp.TenantManagement.Domain.Shared": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.Application.Contracts.dll": {}}}, "Volo.Abp.TenantManagement.Blazor/9.0.1": {"dependencies": {"Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.FeatureManagement.Blazor": "9.0.1", "Volo.Abp.TenantManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.Blazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.Blazor.dll": {}}}, "Volo.Abp.TenantManagement.Blazor.WebAssembly/9.0.1": {"dependencies": {"Volo.Abp.FeatureManagement.Blazor.WebAssembly": "9.0.1", "Volo.Abp.TenantManagement.Blazor": "9.0.1", "Volo.Abp.TenantManagement.HttpApi.Client": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.Blazor.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.Blazor.WebAssembly.dll": {}}}, "Volo.Abp.TenantManagement.Domain/9.0.1": {"dependencies": {"Volo.Abp.AutoMapper": "9.0.1", "Volo.Abp.Caching": "9.0.1", "Volo.Abp.Data": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.TenantManagement.Domain.Shared": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.Domain.dll": {}}}, "Volo.Abp.TenantManagement.Domain.Shared/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Auditing.Contracts": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.Domain.Shared.dll": {}}}, "Volo.Abp.TenantManagement.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.EntityFrameworkCore": "9.0.1", "Volo.Abp.TenantManagement.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.EntityFrameworkCore.dll": {}}}, "Volo.Abp.TenantManagement.HttpApi/9.0.1": {"dependencies": {"Volo.Abp.AspNetCore.Mvc": "9.0.1", "Volo.Abp.FeatureManagement.HttpApi": "9.0.1", "Volo.Abp.TenantManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.HttpApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.HttpApi.dll": {}}}, "Volo.Abp.TenantManagement.HttpApi.Client/9.0.1": {"dependencies": {"Volo.Abp.Http.Client": "9.0.1", "Volo.Abp.TenantManagement.Application.Contracts": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TenantManagement.HttpApi.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TenantManagement.HttpApi.Client.dll": {}}}, "Volo.Abp.TextTemplating/9.0.1": {"dependencies": {"Volo.Abp.TextTemplating.Scriban": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TextTemplating.dll": {}}}, "Volo.Abp.TextTemplating.Core/9.0.1": {"dependencies": {"Volo.Abp.Localization.Abstractions": "9.0.1", "Volo.Abp.VirtualFileSystem": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TextTemplating.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TextTemplating.Core.dll": {}}}, "Volo.Abp.TextTemplating.Scriban/9.0.1": {"dependencies": {"Scriban": "5.10.0", "Volo.Abp.TextTemplating.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.TextTemplating.Scriban.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.TextTemplating.Scriban.dll": {}}}, "Volo.Abp.Threading/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Threading.dll": {}}}, "Volo.Abp.Timing/9.0.1": {"dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "9.0.1", "Volo.Abp.Settings": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Timing.dll": {}}}, "Volo.Abp.UI/9.0.1": {"dependencies": {"Volo.Abp.ExceptionHandling": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.UI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.UI.dll": {}}}, "Volo.Abp.UI.Navigation/9.0.1": {"dependencies": {"Volo.Abp.Authorization": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1", "Volo.Abp.UI": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.UI.Navigation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.UI.Navigation.dll": {}}}, "Volo.Abp.Uow/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Uow.dll": {}}}, "Volo.Abp.Users.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.EventBus": "9.0.1", "Volo.Abp.MultiTenancy": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Users.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Users.Abstractions.dll": {}}}, "Volo.Abp.Users.Domain/9.0.1": {"dependencies": {"Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.Users.Abstractions": "9.0.1", "Volo.Abp.Users.Domain.Shared": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Users.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Users.Domain.dll": {}}}, "Volo.Abp.Users.Domain.Shared/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Users.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Users.Domain.Shared.dll": {}}}, "Volo.Abp.Users.EntityFrameworkCore/9.0.1": {"dependencies": {"Volo.Abp.EntityFrameworkCore": "9.0.1", "Volo.Abp.Users.Domain": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Users.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Users.EntityFrameworkCore.dll": {}}}, "Volo.Abp.Validation/9.0.1": {"dependencies": {"Volo.Abp.Localization": "9.0.1", "Volo.Abp.Validation.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Validation.dll": {}}}, "Volo.Abp.Validation.Abstractions/9.0.1": {"dependencies": {"Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.Validation.Abstractions.dll": {}}}, "Volo.Abp.VirtualFileSystem/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "9.0.0", "Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Volo.Abp.Core": "9.0.1"}, "runtime": {"lib/net9.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net9.0/Volo.Abp.VirtualFileSystem.dll": {}}}, "YamlDotNet/15.1.2": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "1*******", "fileVersion": "15.1.2.0"}}, "compile": {"lib/net8.0/YamlDotNet.dll": {}}}, "MboraMaxNext.Blazor/1.0.0": {"dependencies": {"Blazor-ApexCharts": "5.1.0", "Blazorise.Bootstrap5": "1.6.2", "Blazorise.Charts": "1.6.2", "Blazorise.Components": "1.6.2", "Blazorise.Icons.FontAwesome": "1.6.2", "MboraMaxNext.Contracts": "1.0.0", "Microsoft.AspNetCore.Components.WebAssembly": "9.0.0", "Microsoft.AspNetCore.Components.WebAssembly.DevServer": "9.0.0", "Volo.Abp.Account.HttpApi.Client": "9.0.1", "Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXLiteTheme": "4.0.1", "Volo.Abp.Autofac.WebAssembly": "9.0.1", "Volo.Abp.FeatureManagement.Blazor.WebAssembly": "9.0.1", "Volo.Abp.FeatureManagement.HttpApi.Client": "9.0.1", "Volo.Abp.Identity.Blazor.WebAssembly": "9.0.1", "Volo.Abp.Identity.HttpApi.Client": "9.0.1", "Volo.Abp.OpenIddict.Domain.Shared": "9.0.1", "Volo.Abp.PermissionManagement.Blazor.WebAssembly": "9.0.1", "Volo.Abp.PermissionManagement.HttpApi.Client": "9.0.1", "Volo.Abp.SettingManagement.Blazor.WebAssembly": "9.0.1", "Volo.Abp.SettingManagement.HttpApi.Client": "9.0.1", "Volo.Abp.TenantManagement.Blazor.WebAssembly": "9.0.1", "Volo.Abp.TenantManagement.HttpApi.Client": "9.0.1"}, "runtime": {"MboraMaxNext.Blazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"MboraMaxNext.Blazor.dll": {}}}, "MboraMaxNext.Contracts/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "Volo.Abp.Account.Application.Contracts": "9.0.1", "Volo.Abp.Ddd.Domain": "9.0.1", "Volo.Abp.FeatureManagement.Application.Contracts": "9.0.1", "Volo.Abp.Identity.Application.Contracts": "9.0.1", "Volo.Abp.OpenIddict.Domain.Shared": "9.0.1", "Volo.Abp.PermissionManagement.Application.Contracts": "9.0.1", "Volo.Abp.SettingManagement.Application.Contracts": "9.0.1", "Volo.Abp.TenantManagement.Application.Contracts": "9.0.1", "Volo.Abp.Validation": "9.0.1"}, "runtime": {"MboraMaxNext.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"MboraMaxNext.Contracts.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"compile": {"Microsoft.AspNetCore.Authentication.BearerToken.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization.Reference/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Reference/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"compile": {"Microsoft.AspNetCore.Components.Endpoints.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms.Reference/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web.Reference/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Results/*******": {"compile": {"Microsoft.AspNetCore.Http.Results.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpLogging/*******": {"compile": {"Microsoft.AspNetCore.HttpLogging.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata.Reference/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.OutputCaching/*******": {"compile": {"Microsoft.AspNetCore.OutputCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RateLimiting/*******": {"compile": {"Microsoft.AspNetCore.RateLimiting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RequestDecompression/*******": {"compile": {"Microsoft.AspNetCore.RequestDecompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticAssets/*******": {"compile": {"Microsoft.AspNetCore.StaticAssets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp/*******": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Reference/*******": {"compile": {"Microsoft.Extensions.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Features/*******": {"compile": {"Microsoft.Extensions.Features.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http.Reference/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core.Reference/*******": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/*******": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Reference/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.Reference/*******": {"compile": {"Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives.Reference/*******": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop.Reference/*******": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers.Reference/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/1*******": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext/*******": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers/*******": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent/*******": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable.Reference/*******": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel/*******": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console/*******": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common/*******": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog.Reference/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Asn1/*******": {"compile": {"System.Formats.Asn1.dll": {}}, "compileOnly": true}, "System.Formats.Tar/*******": {"compile": {"System.Formats.Tar.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars/*******": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions/*******": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression/*******": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.AccessControl/*******": {"compile": {"System.IO.FileSystem.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.FileSystem/*******": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines.Reference/*******": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes.AccessControl/*******": {"compile": {"System.IO.Pipes.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions.Reference/*******": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable.Reference/*******": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http/*******": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/*******": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives/*******": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Quic/*******": {"compile": {"System.Net.Quic.dll": {}}, "compileOnly": true}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets/*******": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/*******": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel.Reference/*******": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Reference/*******": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight.Reference/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions.Reference/*******": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata.Reference/*******": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions.Reference/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles/*******": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.JavaScript/*******": {"compile": {"System.Runtime.InteropServices.JavaScript.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader.Reference/*******": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics/*******": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl/*******": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims/*******": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography/*******": {"compile": {"System.Security.Cryptography.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl/*******": {"compile": {"System.Security.Cryptography.OpenSsl.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Principal/*******": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows.Reference/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.Encodings.Web.Reference/*******": {"compile": {"System.Text.Encodings.Web.dll": {}}, "compileOnly": true}, "System.Text.Json.Reference/*******": {"compile": {"System.Text.Json.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.Channels.Reference/*******": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading.Reference/*******": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.RateLimiting/*******": {"compile": {"System.Threading.RateLimiting.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer/*******": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple.Reference/*******": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument/*******": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}}}, "libraries": {"MboraMaxNext.Host/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Asp.Versioning.Abstractions/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpeNZyMdvrHztJwR1sXIUQ+3iioEU97YMBnFA9WLbsPOYhGwDJnqJMmEd8ny7kcmS9OjTHoEuX/bSXXY3brIFA==", "path": "asp.versioning.abstractions/8.1.0", "hashPath": "asp.versioning.abstractions.8.1.0.nupkg.sha512"}, "Asp.Versioning.Http/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xu4xF62Cu9JqYi/CTa2TiK5kyHoa4EluPynj/bPFWDmlTIPzuJQbBI5RgFYVRFHjFVvWMoA77acRaFu7i7Wzqg==", "path": "asp.versioning.http/8.1.0", "hashPath": "asp.versioning.http.8.1.0.nupkg.sha512"}, "Asp.Versioning.Mvc/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BMAJM2sGsTUw5FQ9upKQt6GFoldWksePgGpYjl56WSRvIuE3UxKZh0gAL+wDTIfLshUZm97VCVxlOGyrcjWz9Q==", "path": "asp.versioning.mvc/8.1.0", "hashPath": "asp.versioning.mvc.8.1.0.nupkg.sha512"}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-a90gW/4TF/14Bjiwg9LqNtdKGC4G3gu02+uynq3bCISfQm48km5chny4Yg5J4hixQPJUwwJJ9Do1G+jM8L9h3g==", "path": "asp.versioning.mvc.apiexplorer/8.1.0", "hashPath": "asp.versioning.mvc.apiexplorer.8.1.0.nupkg.sha512"}, "AsyncKeyedLock/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-N7KFQAGk3/bx5WrDDxHWcVXd63INae1spNCYTzHPRnw3i3PC/82/SnGEbnD7zld+5hvRsm9Z8xyS7GJLRoV8lA==", "path": "asynckeyedlock/7.0.1", "hashPath": "asynckeyedlock.7.0.1.nupkg.sha512"}, "Autofac/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-O2QT+0DSTBR2Ojpacmcj3L0KrnnXTFrwLl/OW1lBUDiHhb89msHEHNhTA8AlK3jdFiAfMbAYyQaJVvRe6oSBcQ==", "path": "autofac/8.1.0", "hashPath": "autofac.8.1.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/10.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZjR/onUlP7BzQ7VBBigQepWLAyAzi3VRGX3pP6sBqkPRiT61fsTZqbTpRUKxo30TMgbs1o3y6bpLbETix4SJog==", "path": "autofac.extensions.dependencyinjection/10.0.0", "hashPath": "autofac.extensions.dependencyinjection.10.0.0.nupkg.sha512"}, "Autofac.Extras.DynamicProxy/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Da6Szv7A1LK/cTdeoyqS45zb/BC5vep8+86f6C1oh2UhZaYtiijlNfLWamp3lxe0uUQ33kFe1dDCjsvfwJWzLg==", "path": "autofac.extras.dynamicproxy/7.1.0", "hashPath": "autofac.extras.dynamicproxy.7.1.0.nupkg.sha512"}, "AutoMapper/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/Fx1SbJ16qS7dU4i604Sle+U9VLX+WSNVJggk6MupKVkYvvBm4XqYaeFuf67diHefHKHs50uQIS2YEDFhPCakQ==", "path": "automapper/13.0.1", "hashPath": "automapper.13.0.1.nupkg.sha512"}, "Blazor-ApexCharts/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-hG8PcZFyZ/eCVMTt5QnrsU0sofL9cskg6LBU1MsQpw4yS+7k/q5LuGoHxy095J3AUSXJrYu7GJmwu1yv6t7Hqw==", "path": "blazor-apexcharts/5.1.0", "hashPath": "blazor-apexcharts.5.1.0.nupkg.sha512"}, "Blazorise/1.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-RqNpIqmamWprykwZEmMg+afwxbc48II3Uf2CItkwWnpzvhgnb0KQDCl77m+3AHs6yBPrGIq6YseudGX4YretkQ==", "path": "blazorise/1.6.2", "hashPath": "blazorise.1.6.2.nupkg.sha512"}, "Blazorise.Bootstrap5/1.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-k52ecU6J/GdTRvamb90pwxoE6URSxJaRgr2aT2eS8WGeBw7mG0nBrTZ17BkruRALH0pDKacMlqALBtKGm/wTcA==", "path": "blazorise.bootstrap5/1.6.2", "hashPath": "blazorise.bootstrap5.1.6.2.nupkg.sha512"}, "Blazorise.Charts/1.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-lKfRRabG4hqzwEoYhtp75qmgNQwrln8faq129xK4GGRMiGDrCNiIJ6wvTcQRZMQAkS8f5zWaBO2K63PR8OQloQ==", "path": "blazorise.charts/1.6.2", "hashPath": "blazorise.charts.1.6.2.nupkg.sha512"}, "Blazorise.Components/1.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-aXUxt606LzCyiUp8AcEQf8qE2KMtdqGCcI46hZ+p8iwAbqYly2YoCLzeTkALMEMcyL0igfqKhl9mxLHSwOINcA==", "path": "blazorise.components/1.6.2", "hashPath": "blazorise.components.1.6.2.nupkg.sha512"}, "Blazorise.DataGrid/1.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-dd8tEGhqjGQ/THhOrvhXvk8LOOyHqfPyxS4tOhHWJsJ9Pxnn0PuJaCzyTalYlIgrHPV6Bi3RWU9u9Cm3j+Leng==", "path": "blazorise.datagrid/1.6.2", "hashPath": "blazorise.datagrid.1.6.2.nupkg.sha512"}, "Blazorise.Icons.FontAwesome/1.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-pHw8ukfky6Zj/mu/zKivYvKx0EPAIqFnYrwHjcaeC4T+xxOV3WIDSPacl5zWdY7rMzYiwn0qzgcTYy40UpUv8g==", "path": "blazorise.icons.fontawesome/1.6.2", "hashPath": "blazorise.icons.fontawesome.1.6.2.nupkg.sha512"}, "Blazorise.Licensing/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-h7jkAF+yyIKnUqFYivaxIySDma1zLV+0xcg52r2gIe772BLQWnBaANjpMBKXxRlYiEyOGPQWjMHJWs3dL/TgmA==", "path": "blazorise.licensing/1.2.0", "hashPath": "blazorise.licensing.1.2.0.nupkg.sha512"}, "Blazorise.Snackbar/1.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZDqlSet1lNNdjoFUlUSn5VxYp2ENS1QyBWbCok/Lqk0jCi+AjbfDf2HVa1ZJmM9VIeRlM4UxRYjhmcIV9vojlg==", "path": "blazorise.snackbar/1.6.2", "hashPath": "blazorise.snackbar.1.6.2.nupkg.sha512"}, "Bogus/35.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-QPJ8zLL0ScEunEoUNhMDigD4ykA/n7vXCZUQdGCFGHBfP2mNYjiCzR9Cj12eaFaDlwkhIC1HKQZihWl8OgbLsw==", "path": "bogus/35.6.1", "hashPath": "bogus.35.6.1.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "path": "castle.core.asyncinterceptor/2.1.0", "hashPath": "castle.core.asyncinterceptor.2.1.0.nupkg.sha512"}, "ClosedXML/0.104.2": {"type": "package", "serviceable": true, "sha512": "sha512-gOkSjQ152MhpKmw70cBkJV+FnaZAWzDwM36luRf/7FlWYnNeH++9XYdGTd0Y4KQlVPkKVxy948M5MMsnsGC4GQ==", "path": "closedxml/0.104.2", "hashPath": "closedxml.0.104.2.nupkg.sha512"}, "ClosedXML.Parser/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-w+/0tsxABS3lkSH8EUlA7IGme+mq5T/Puf3DbOiTckmSuUpAUO2LK29oXYByCcWkBv6wcRHxgWlQb1lxkwI0Tw==", "path": "closedxml.parser/1.2.0", "hashPath": "closedxml.parser.1.2.0.nupkg.sha512"}, "DeviceDetector.NET/6.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-jSquN8aHkpImKKB4c0D+aMq49ues3KFizJ5J8exG8au/+kZ7VHC4/Sjlmaa3Xfd1Dw5hn+dIcZmyMpYqBl/fsg==", "path": "devicedetector.net/6.3.3", "hashPath": "devicedetector.net.6.3.3.nupkg.sha512"}, "DocumentFormat.OpenXml/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-2z9QBzeTLNNKWM9SaOSDMegfQk/7hDuElOsmF77pKZMkFRP/GHA/W/4yOAQD9kn15N/FsFxHn3QVYkatuZghiA==", "path": "documentformat.openxml/3.1.1", "hashPath": "documentformat.openxml.3.1.1.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-6APEp/ElZV58S/4v8mf4Ke3ONEDORs64MqdD64Z7wWpcHANB9oovQsGIwtqjnKihulOj7T0a6IxHIHOfMqKOng==", "path": "documentformat.openxml.framework/3.1.1", "hashPath": "documentformat.openxml.framework.3.1.1.nupkg.sha512"}, "ExcelNumberFormat/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "path": "excelnumberformat/1.1.0", "hashPath": "excelnumberformat.1.1.0.nupkg.sha512"}, "Grpc.Core.Api/2.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-3ZpDzd5iMRBUv22hcvBqhL1yFFNfa9AccG8koco0ezbO/NMt3E0GsTf1BeIZ2G6ceFZR3fEMawpiJAlXhDEpQA==", "path": "grpc.core.api/2.35.0", "hashPath": "grpc.core.api.2.35.0.nupkg.sha512"}, "Grpc.Net.Client/2.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-UJkhLPa80jR0G6VsZmmSbKQBsvmxKPY+N2UvN/cEWZXezboRoSyk8h4Yb3t/BEHsRkjNhMqw4UPpr2INUK1HlA==", "path": "grpc.net.client/2.34.0", "hashPath": "grpc.net.client.2.34.0.nupkg.sha512"}, "Grpc.Net.Common/2.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-dxVKzogjSejTV0hGeL61fmw6BSzF5uW4PBDWnx8LJFn41drgIPIXOoCJVqSAZ39lkGjTZlxtcwV5Vy6zqTf5Gw==", "path": "grpc.net.common/2.34.0", "hashPath": "grpc.net.common.2.34.0.nupkg.sha512"}, "Humanizer.Core/3.0.0-beta.54": {"type": "package", "serviceable": true, "sha512": "sha512-V1LqoHpfgEUoYJHiboaL6rZS/Wfr/K7xlsL0/SjwWtuFtV1d3bV8sTEPjURHmfxySND8KgQex5WuuuwMBraDog==", "path": "humanizer.core/3.0.0-beta.54", "hashPath": "humanizer.core.3.0.0-beta.54.nupkg.sha512"}, "IdentityModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-to99aLL5Gev1GOb2gUao/UZXT/uXMyjEmHPNrf/vJI2HBD1LMCTeC4SBCe/cqMIB12V9v+eSieq7ff0lju9pOQ==", "path": "identitymodel/7.0.0", "hashPath": "identitymodel.7.0.0.nupkg.sha512"}, "JetBrains.Annotations/2024.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-GNnqCFW/163p1fOehKx0CnAqjmpPrUSqrgfHM6qca+P+RN39C9rhlfZHQpJhxmQG/dkOYe/b3Z0P8b6Kv5m1qw==", "path": "jetbrains.annotations/2024.2.0", "hashPath": "jetbrains.annotations.2024.2.0.nupkg.sha512"}, "Lambda2Js/3.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-yxPJ5n+jUdjTZSItFj2wx5uFGm8iN6Gt42apqnEq0Zm5R6GgueZFFl+GDSe/CEogMSaBRw8kRG3yZm8ABFWXGQ==", "path": "lambda2js/3.1.4", "hashPath": "lambda2js.3.1.4.nupkg.sha512"}, "LiteDB/5.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-O8XPBptE4SygW2SN6skqg/VDVTrjpJ0p6+i7Cp8x8razbu2ORLSd6ep3JdhDIdL6h57Bcxv2BuVaN70IKpXI0Q==", "path": "litedb/5.0.19", "hashPath": "litedb.5.0.19.nupkg.sha512"}, "MagicOnion.Abstractions/5.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-iwoG7nIg4z5jhYnBg+1HElbcTRJ9Hjc9cVfQ9+9DfY/kDWqVcFvKZGHTPCVxRzYa9aOPfDH8nwbTWMocontqqQ==", "path": "magiconion.abstractions/5.1.8", "hashPath": "magiconion.abstractions.5.1.8.nupkg.sha512"}, "MagicOnion.Client/5.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-fDGM7GrBrpc/oL37LgmjRBZLBQX+5PKL9ySQncImcXKkRt7SdXXaxecTnsON6btXz0PbEhfRSMUuvqs2CfEurw==", "path": "magiconion.client/5.1.8", "hashPath": "magiconion.client.5.1.8.nupkg.sha512"}, "MagicOnion.Shared/5.1.8": {"type": "package", "serviceable": true, "sha512": "sha512-IMipX7Sjda2uOUaosiPUExO/PW984AwkbiGy4qEoDK8s19AVR2/QJeBpcoTR3P8REUzNWIQkc8MhkC04h8q3NA==", "path": "magiconion.shared/5.1.8", "hashPath": "magiconion.shared.5.1.8.nupkg.sha512"}, "MessagePack/2.2.85": {"type": "package", "serviceable": true, "sha512": "sha512-3SqAgwNV5LOf+ZapHmjQMUc7WDy/1ur9CfFNjgnfMZKCB5CxkVVbyHa06fObjGTEHZI7mcDathYjkI+ncr92ZQ==", "path": "messagepack/2.2.85", "hashPath": "messagepack.2.2.85.nupkg.sha512"}, "MessagePack.Annotations/2.2.85": {"type": "package", "serviceable": true, "sha512": "sha512-YptRsDCQK35K5FhmZ0LojW4t8I6DpetLfK5KG8PVY2f6h7/gdyr8f4++xdSEK/xS6XX7/GPvEpqszKVPksCsiQ==", "path": "messagepack.annotations/2.2.85", "hashPath": "messagepack.annotations.2.2.85.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-afrTDM8viZRruJGNkGa0pMhNqgjWBLln3DBPYrQaklOQn+wE9B5ZvOpi7l8l68JEwsBUVKteKyiY1ivPlK6kQw==", "path": "microsoft.aspnetcore.authentication.openidconnect/9.0.0", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qDJlBC5pUQ/3o6/C6Vuo9CGKtV5TAe5AdKeHvDR2bgmw8vwPxsAy3KG5eU0i1C+iAUNbmq+iDTbiKt16f9pRiA==", "path": "microsoft.aspnetcore.authorization/9.0.0", "hashPath": "microsoft.aspnetcore.authorization.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xKzY0LRqWrwuPVzKIF9k1kC21NrLmIE2qPhhKlInEAdYqNe8qcMoPWZy7fo1uScHkz5g73nTqDDra3+aAV7mTQ==", "path": "microsoft.aspnetcore.components/9.0.0", "hashPath": "microsoft.aspnetcore.components.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-maOE1qlJ9hf1Fb7PhFLw9bgP9mWckuDOcn1uKNt9/msdJG2YHl3cPRHojYa6CxliGHIXL8Da4qPgeUc4CaOoeg==", "path": "microsoft.aspnetcore.components.analyzers/9.0.0", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Authorization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LD5ApnnUgMAyFDMKXqhyKFksnnxicGxE15dvC6rnOynFzj11Rvf7bENjTP9HUIbD64MYug+wlhl06A4nicw+RQ==", "path": "microsoft.aspnetcore.components.authorization/9.0.0", "hashPath": "microsoft.aspnetcore.components.authorization.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-orHGxDkbAa9syuaLVtZWOhNC8IddnCsDqpFaKjBj4zxe+B8cd6kcNf/t4Lv5hWBQ7mODiRCzEfKBnpU+GCHvbw==", "path": "microsoft.aspnetcore.components.forms/9.0.0", "hashPath": "microsoft.aspnetcore.components.forms.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZfJwwV05T+268cnJsO6yfi9oXYLe3ATRAEk0VZgBMptA5HVsduIsnFLjhNOYT7+I8NolxDEx1CEW8yKe5xTb6Q==", "path": "microsoft.aspnetcore.components.web/9.0.0", "hashPath": "microsoft.aspnetcore.components.web.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RwDygFGa1NdKh7N4S6P2xgxB0LeluLss/iZRdNnmOwN1zgpdLH9AnCnTIgDBAW7rhDjcPGrcjsDLNivwDRYwEQ==", "path": "microsoft.aspnetcore.components.webassembly/9.0.0", "hashPath": "microsoft.aspnetcore.components.webassembly.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.Authentication/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dWGMHCh3/MchcPTvwz8FBd5O1FBE3Dxq6wWayi2xypgADJDTmpQnmMedN90sNVfekXQheAofYh0aPv6+Rt8Zlw==", "path": "microsoft.aspnetcore.components.webassembly.authentication/9.0.0", "hashPath": "microsoft.aspnetcore.components.webassembly.authentication.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k0i95CLhe9h6pfQUCvOksSY3Kz7ukxWLrXrADfrYzlvawCyO8oq+bviOTevNl2cS8RA8hmyWK0we3bZ1BNtCoA==", "path": "microsoft.aspnetcore.components.webassembly.devserver/9.0.0", "hashPath": "microsoft.aspnetcore.components.webassembly.devserver.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.Server/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-916w5kN0c5ytEuHAk6e1W8JLixlcEhAuKJLSBGdZUaVBuz/3E6r6qnGCdR9cyPvu2D+Stkbfv60Z05gwF/JnDA==", "path": "microsoft.aspnetcore.components.webassembly.server/9.0.0", "hashPath": "microsoft.aspnetcore.components.webassembly.server.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M1dzTEl+2+RqT4vWcqEpWasPXHd58wC93U7QMlmPSmx+qixyVxCQjZ183wr7Wa68b4pF7wC501MU9rdA0ZNhMg==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9X4cx2IHNpYb9ka984BjDpJnKkindW17Z2kR/RI5pbTcbVUVMJjiAKnBhAqH24KtAEf1AU64LD60byzCn0/n8w==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-X81C891nMuWgzNHyZ0C3s+blSDxRHzQHDFYQoOKtFvFuxGq3BbkLbc5CfiCqIzA/sWIfz6u8sGBgwntQwBJWBw==", "path": "microsoft.aspnetcore.metadata/9.0.0", "hashPath": "microsoft.aspnetcore.metadata.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M0h+ChPgydX2xY17agiphnAVa/Qh05RAP8eeuqGGhQKT10claRBlLNO6d2/oSV8zy0RLHzwLnNZm5xuC/gckGA==", "path": "microsoft.aspnetcore.mvc.razor.extensions/6.0.0", "hashPath": "microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eonBqJduSWG7Sdk8Y8FZ99Raj4UgEQ8/8IMxm6fuv8WlD3r+ZkPVBX9zKMRBa4lAyq+sxU9pu1FDGT2kUtTD8w==", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/9.0.0", "hashPath": "microsoft.aspnetcore.mvc.razor.runtimecompilation.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yCtBr1GSGzJrrp1NJUb4ltwFYMKHw/tJLnIDvg9g/FnkGIEzmE19tbCQqXARIJv5kdtBgsoVIdGLL+zmjxvM/A==", "path": "microsoft.aspnetcore.razor.language/6.0.0", "hashPath": "microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nUtmKeQ2cBiiZNYkl4KpQO8C2YP/EAc/r6PymeaRnO5AxBtOs6GkWXwZh+65vjfjXtlIxZeexGzXA3vy7+G7Vw==", "path": "microsoft.aspnetcore.webutilities/9.0.0", "hashPath": "microsoft.aspnetcore.webutilities.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-owmu2Cr3IQ8yQiBleBHlGk8dSQ12oaF2e7TpzwJKEl4m84kkZJjEY1n33L67Y3zM5jPOjmmbdHjbfiL0RqcMRQ==", "path": "microsoft.bcl.asyncinterfaces/9.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "path": "microsoft.bcl.timeprovider/8.0.1", "hashPath": "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uqdzuQXxD7XrJCbIbbwpI/LOv0PBJ9VIR0gdvANTHOfK5pjTaCir+XcwvYvBZ5BIzd0KGzyiamzlEWw1cK1q0w==", "path": "microsoft.codeanalysis.razor/6.0.0", "hashPath": "microsoft.codeanalysis.razor.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wpG+nfnfDAw87R3ovAsUmjr3MZ4tYXf6bFqEPVAIKE6IfPml3DS//iX0DBnf8kWn5ZHSO5oi1m4d/Jf+1LifJQ==", "path": "microsoft.entityframeworkcore/9.0.0", "hashPath": "microsoft.entityframeworkcore.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fnmifFL8KaA4ZNLCVgfjCWhZUFxkrDInx5hR4qG7Q8IEaSiy/6VOSRFyx55oH7MV4y7wM3J3EE90nSpcVBI44Q==", "path": "microsoft.entityframeworkcore.abstractions/9.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qje+DzXJOKiXF72SL0XxNlDtTkvWWvmwknuZtFahY5hIQpRKO59qnGuERIQ3qlzuq5x4bAJ8WMbgU5DLhBgeOQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pqo8I+yHJ3VQrAoY0hiSncf+5P7gN/RkNilK5e+/K/yKh+yAWxdUAI6t0TG26a9VPlCa9FhyklzyFvRyj3YG9A==", "path": "microsoft.entityframeworkcore.design/9.0.0", "hashPath": "microsoft.entityframeworkcore.design.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-j+msw6fWgAE9M3Q/5B9Uhv7pdAdAQUvFPJAiBJmoy+OXvehVbfbCE8ftMAa51Uo2ZeiqVnHShhnv4Y4UJJmUzA==", "path": "microsoft.entityframeworkcore.relational/9.0.0", "hashPath": "microsoft.entityframeworkcore.relational.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qjw+3/CaWiWnyVblvKHY11rQKH5eHQDSbtxjgxVhxGJrOpmjZ3JxtD0pjwkr4y/ELubsXr6xDfBcRJSkX/9hWQ==", "path": "microsoft.entityframeworkcore.tools/9.0.0", "hashPath": "microsoft.entityframeworkcore.tools.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FPWZAa9c0H4dvOj351iR1jkUIs4u9ykL4Bm592yhjDyO5lCoWd+TMAHx2EMbarzUvCvgjWjJIoC6//Q9kH6YhA==", "path": "microsoft.extensions.caching.abstractions/9.0.0", "hashPath": "microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Hybrid/9.0.0-preview.7.24406.2": {"type": "package", "serviceable": true, "sha512": "sha512-RxwIJ1QmnoZNxN94V31jpjZzLeC07ZnC27kGbTKwIc01XITa/NDbVL43NEIWvsLLxguIPg7X76NAzV7FhO+2eQ==", "path": "microsoft.extensions.caching.hybrid/9.0.0-preview.7.24406.2", "hashPath": "microsoft.extensions.caching.hybrid.9.0.0-preview.7.24406.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbnPX/JQ0pETRSUG9fNPBvpIq42Aufvs15gGYyNIMhCun9yhmWihz0WgsI7bSDPjxWTKBf8oX/zv6v2uZ3W9OQ==", "path": "microsoft.extensions.caching.memory/9.0.0", "hashPath": "microsoft.extensions.caching.memory.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "path": "microsoft.extensions.configuration/9.0.0", "hashPath": "microsoft.extensions.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qD+hdkBtR9Ps7AxfhTJCnoVakkadHgHlD1WRN0QHGHod+SDuca1ao1kF4G2rmpAz2AEKrE2N2vE8CCCZ+ILnNw==", "path": "microsoft.extensions.configuration.commandline/9.0.0", "hashPath": "microsoft.extensions.configuration.commandline.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v5R638eNMxksfXb7MFnkPwLPp+Ym4W/SIGNuoe8qFVVyvygQD5DdLusybmYSJEr9zc1UzWzim/ATKeIOVvOFDg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4EK93Jcd2lQG4GY6PAw8jGss0ZzFP0vPc1J85mES5fKNuDTqgFXHba9onBw2s18fs3I4vdo2AWyfD1mPAxWSQQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WiTK0LrnsqmedrbzwL7f4ZUo+/wByqy2eKab39I380i2rd8ImfCRMrtkqJVGDmfqlkP/YzhckVOwPc5MPrSNpg==", "path": "microsoft.extensions.configuration.json/9.0.0", "hashPath": "microsoft.extensions.configuration.json.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FShWw8OysquwV7wQHYkkz0VWsJSo6ETUu4h7tJRMtnG0uR+tzKOldhcO8xB1pGSOI3Ng6v3N1Q94YO8Rzq1P6A==", "path": "microsoft.extensions.configuration.usersecrets/9.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0CF9ZrNw5RAlRfbZuVIvzzhP8QeWqHiUmMBU/2H7Nmit8/vwP3/SbHeEctth7D4Gz2fBnEbokPc1NU8/j/1ZLw==", "path": "microsoft.extensions.diagnostics/9.0.0", "hashPath": "microsoft.extensions.diagnostics.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1K8P7XzuzX8W8pmXcZjcrqS6x5eSSdvhQohmcpgiQNY/HlDAlnrhR9dvlURfFz428A+RTCJpUyB+aKTA6AgVcQ==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IjNhwxaJ1nvl6K49AhaNLTXI0RgmGboWwqBv2NttC8RXSOjgucP8qYapCXrJS/Xf9hSHILJ7NJNdY9F6QjPqQA==", "path": "microsoft.extensions.fileproviders.composite/9.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Ev1goLIvggLF6uCs6oZvdr9JM+2b1Zj+4FLdBWNW5iw3tm2BymVIb0yMsjnQTBWL7YUmqVWH3u45hSqOfvuqg==", "path": "microsoft.extensions.fileproviders.embedded/9.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yUKJgu81ExjvqbNWqZKshBbLntZMbMVz/P7Way2SBx7bMqA08Mfdc9O7hWDKAiSp+zPUGT6LKcSCQIPeDK+CCw==", "path": "microsoft.extensions.hosting.abstractions/9.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DqI4q54U4hH7bIAq9M5a/hl5Odr/KBAoaZ0dcT4OgutD8dook34CbkvAfAIzkMVjYXiL+E5ul9etwwqiX4PHGw==", "path": "microsoft.extensions.http/9.0.0", "hashPath": "microsoft.extensions.http.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cQjUs8PIheIMALzrf/e4gW6A/yOK8XYBxeEmAfLvVIaV9lsBGvVT0zjEZ1KPQDJ9nUeQ9uAw077J7LPUwv8wA==", "path": "microsoft.extensions.identity.core/9.0.0", "hashPath": "microsoft.extensions.identity.core.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Up8Juy8Bh+vL+fXmMWsoSg/G6rszmLFiF44aI2tpOMJE7Ln4D9s37YxOOm81am4Z+V7g8Am3AgVwHYJzi+cL/g==", "path": "microsoft.extensions.localization/9.0.0", "hashPath": "microsoft.extensions.localization.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wc7PaRhPOnio5Csj80b3UgBWA5l6bp28EhGem7gtfpVopcwbkfPb2Sk8Cu6eBnIW3ZNf1YUgYJzwtjzZEM8+iw==", "path": "microsoft.extensions.localization.abstractions/9.0.0", "hashPath": "microsoft.extensions.localization.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob3FXsXkcSMQmGZi7qP07EQ39kZpSBlTcAZLbJLdI4FIf0Jug8biv2HTavWmnTirchctPlq9bl/26CXtQRguzA==", "path": "microsoft.extensions.options.configurationextensions/9.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jZ7UKZNu2tDS2Ft9PxxOdJYUWo6nBEQnv+yz69GpCr4Fs677aq/LP82gp/NOgI5hPIlxIYP1ynLCH1ruGfw2rw==", "path": "microsoft.identitymodel.abstractions/8.1.0", "hashPath": "microsoft.identitymodel.abstractions.8.1.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-p77XmuOb6Vt9Zi0jowBK5zk8YoIQs8kK2CSKaV0ofkNLKSraKXlndJ9gBT0ojP7BOQP7vAWTgVVgB8Kg5pmXpg==", "path": "microsoft.identitymodel.jsonwebtokens/8.1.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.1.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qrm6jLmzeNnWwKc+KoJT8DQgnVXsNR3LBSU2LXnA9RDs8eflBNrQOifxJn9r4k5+rdRq3mUu3GJNGKlWIQzguA==", "path": "microsoft.identitymodel.logging/8.1.0", "hashPath": "microsoft.identitymodel.logging.8.1.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tQ21tUUKjG5Hzv/Rxmgg4f2LZo+nas1OJJdhHmLaU9c7l72lgQgP7luXlBnJOS2Lotd4DIj3ZE+lFVaVmkniag==", "path": "microsoft.identitymodel.tokens/8.1.0", "hashPath": "microsoft.identitymodel.tokens.8.1.0.nupkg.sha512"}, "Microsoft.JSInterop/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-efQKxKUPe8OuH0hRiYsvBJkhhPzIYFNcr9+3wanQ7Bch/wr1JWNd90GYiPLtkSHepE1zMEoaLkAxi5N5/eyC4Q==", "path": "microsoft.jsinterop/9.0.0", "hashPath": "microsoft.jsinterop.9.0.0.nupkg.sha512"}, "Microsoft.JSInterop.WebAssembly/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5TjNuiavEVk+4UFTZVOKMlpq1qgYW/Vd6N7lNsHJR9kOlhP6S2GtiKdbLn//Aw/22lx3jDJTkekc54F5S8JC4g==", "path": "microsoft.jsinterop.webassembly/9.0.0", "hashPath": "microsoft.jsinterop.webassembly.9.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Haa8791nKzjZtck7KOzM4hgoyvwBfVzkyTLdmEVRm3FdHB1iahQZNiou17hSF5PlOQT0hr4BAQYeZYvfs4BZMQ==", "path": "microsoft.net.http.headers/9.0.0", "hashPath": "microsoft.net.http.headers.9.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.14": {"type": "package", "serviceable": true, "sha512": "sha512-tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw==", "path": "microsoft.openapi/1.6.14", "hashPath": "microsoft.openapi.1.6.14.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "Npgsql/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zu1nCRt0gWP/GR0reYgg0Bl5o8qyNV7mVAgzAbVLRiAd1CYXcf/9nrubPH0mt93u8iGTKmYqWaLVECEAcE6IfQ==", "path": "npgsql/9.0.0", "hashPath": "npgsql.9.0.0.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ObngKFRLMBAeMqQzK7SC0Q6WZtWw0imPmEkVPo12yLVF3fioz2TN+w0mhNMJ5cVd/sLB2u+jei0bmA9sDMtkMw==", "path": "npgsql.entityframeworkcore.postgresql/9.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.0.nupkg.sha512"}, "NUglify/1.21.9": {"type": "package", "serviceable": true, "sha512": "sha512-ULyI/scrIRAo2In6cnaCc/QkWUt+wB6pBVt5lrVddOKyamsCAm1XgArkk4px9tVn6SipjhbTt4M38QPlpoET+g==", "path": "nuglify/1.21.9", "hashPath": "nuglify.1.21.9.nupkg.sha512"}, "OpenIddict.Abstractions/5.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-ABzxIPp/swvGSPuyP4v+t8UKo8gRTzLQakIA+vg/h4j2nCrHpL7fPZaydj2Kzn0a6DKWUVAKw6eZczF/2dz1VQ==", "path": "openiddict.abstractions/5.8.0", "hashPath": "openiddict.abstractions.5.8.0.nupkg.sha512"}, "OpenIddict.Core/5.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-6OQhDdFbyAfRgmE+Lh59YggJhhOX9AQ28gRFSZ55HAluhsknvq0B9ZWKsuxEEBSyr6m+A8j6HgFH9JfbI9+q4Q==", "path": "openiddict.core/5.8.0", "hashPath": "openiddict.core.5.8.0.nupkg.sha512"}, "OpenIddict.Server/5.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-CpS78OmkfHD/sfwqTeQ4PIjdLfqHTKItRe0eB4y9U6YV9JcPu3McH1jkNg8CAlgUP/VpkEHti7eBFmZGdvLANw==", "path": "openiddict.server/5.8.0", "hashPath": "openiddict.server.5.8.0.nupkg.sha512"}, "OpenIddict.Server.AspNetCore/5.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-2MW98cPcSI53Pe9Yv3Z4a9L4sgf3Zm4LEYg3bAH9Lk9Ii1wBa13nzUvXwA36Wyjyv7Av2yP31SYRomeEH7LgCA==", "path": "openiddict.server.aspnetcore/5.8.0", "hashPath": "openiddict.server.aspnetcore.5.8.0.nupkg.sha512"}, "OpenIddict.Validation/5.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-VBh+r+15IH5jVs7auzaIqIGN3ExP0uZOkVBMUz/KipYCMw9C4Usa4Pd6VcFNbcCqWPMrl9RLw6rw0pCKhY6E9g==", "path": "openiddict.validation/5.8.0", "hashPath": "openiddict.validation.5.8.0.nupkg.sha512"}, "OpenIddict.Validation.AspNetCore/5.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/52/WeD/9faYr7B6XfzBSM2C8psRFiqbdwSxxiT+fIgINzER+UVEwHrIW/JSW0JjcLpD4NZ72S/rBxlWgWSzVQ==", "path": "openiddict.validation.aspnetcore/5.8.0", "hashPath": "openiddict.validation.aspnetcore.5.8.0.nupkg.sha512"}, "OpenIddict.Validation.ServerIntegration/5.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-5+gSgPK/HXR4g3S3q6TB+cXSXS6ESWaw/l2e11adM+KufSheMFPblBBDA/hax7hYj1DsrF4o8BxQT2SjPdUrRA==", "path": "openiddict.validation.serverintegration/5.8.0", "hashPath": "openiddict.validation.serverintegration.5.8.0.nupkg.sha512"}, "Polly/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qfe/eoHQ9tJOGVmJ/y+fUvK5nHDrS0EZ/DguwYXGN8gnDwlUWdmPqUqoj0PJvg8awELac+9YL7W0GOd1vTORg==", "path": "polly/8.4.2", "hashPath": "polly.8.4.2.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "RBush/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-j3GeRxxLUQdc+UrZnvythdQxi3bd8ayn87VDjfGXrvfodF550n9wR6SgQvpo+YiAv3GJezsu6lK0l47rRqnbdg==", "path": "rbush/4.0.0", "hashPath": "rbush.4.0.0.nupkg.sha512"}, "Scriban/5.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-qk2W8wQpm7mykWrEi9fhjC7uVapT2bkweMuMqebrF7gaVMt0WjmZzyVVTpom5cUsc3ddMDpo95SkNcTWGo+L6Q==", "path": "scriban/5.10.0", "hashPath": "scriban.5.10.0.nupkg.sha512"}, "Serilog/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Vehq4uNYtURe/OnHEpWGvMgrvr5Vou7oZLdn3BuEH5FSCeHXDpNJtpzWoqywXsSvCTuiv0I65mZDRnJSeUvisA==", "path": "serilog/4.0.2", "hashPath": "serilog.4.0.2.nupkg.sha512"}, "Serilog.AspNetCore/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-LNUd1bHsik2E7jSoCQFdeMGAWXjH7eUQ6c2pqm5vl+jGqvxdabYXxlrfaqApjtX5+BfAjW9jTA2EKmPwxknpIA==", "path": "serilog.aspnetcore/8.0.2", "hashPath": "serilog.aspnetcore.8.0.2.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "path": "serilog.formatting.compact/2.0.0", "hashPath": "serilog.formatting.compact.2.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hn8HCAmupon7N0to20EwGeNJ+L3iRzjGzAHIl8+8CCFlEkVedHvS6NMYMb0VPNMsDgDwOj4cPBPV6Fc2hb0/7w==", "path": "serilog.settings.configuration/8.0.2", "hashPath": "serilog.settings.configuration.8.0.2.nupkg.sha512"}, "Serilog.Sinks.Async/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-a+kTyUUxPAOZWKJiNbDqCPMiP0BWBWzObyTpRmGLrgCecgc/YJ+HqYGjsQoS6Sj9cRVXB9hH5O1mTZ0DiewG2w==", "path": "serilog.sinks.async/2.0.0", "hashPath": "serilog.sinks.async.2.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IZ6bn79k+3SRXOBpwSOClUHikSkp2toGPCZ0teUkscv4dpDg9E2R2xVsNkLmwddE4OpNVO3N0xiYsAH556vN8Q==", "path": "serilog.sinks.console/5.0.0", "hashPath": "serilog.sinks.console.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-JN6ccH37QKtNOwBrvSxc+jBYIB+cw6RlZie2IKoJhjjf6HzBH+2kPJCpxmJ5EHIqmxvq6aQG+0A8XklGx9rAxA==", "path": "swashbuckle.aspnetcore/6.8.1", "hashPath": "swashbuckle.aspnetcore.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-eOkdM4bsWBU5Ty3kWbyq5O9L+05kZT0vOdGh4a92vIb/LLQGQTPLRHXuJdnUBNIPNC8XfKWfSbtRfqzI6nnbqw==", "path": "swashbuckle.aspnetcore.swagger/6.8.1", "hashPath": "swashbuckle.aspnetcore.swagger.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-TjBPxsN0HeJzxEXZYeDXBNNMSyhg+TYXtkbwX+Cn8GH/y5ZeoB/chw0p71kRo5tR2sNshbKwL24T6f9pTF9PHg==", "path": "swashbuckle.aspnetcore.swaggergen/6.8.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.8.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-lpEszYJ7vZaTTE5Dp8MrsbSHrgDfjhDMjzW1qOA1Xs1Dnj3ZRBJAcPZUTsa5Bva+nLaw91JJ8OI8FkSg8hhIyA==", "path": "swashbuckle.aspnetcore.swaggerui/6.8.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.8.1.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QhkXUl2gNrQtvPmtBTQHb0YsUrDiDQ2QS09YbtTTiSjGcf7NBqtYbrG/BE06zcBPCKEwQGzIv13IVdXNOSub2w==", "path": "system.collections.immutable/9.0.0", "hashPath": "system.collections.immutable.9.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-2Na5IOk7Tr7gmBDyPOSpFPi8DXVHfJofbmp6A0L3NSuJz6n3n5kx3CgBD1WRvSaSLu0QHjvrzv6rpghiqmFgSg==", "path": "system.identitymodel.tokens.jwt/8.1.0", "hashPath": "system.identitymodel.tokens.jwt.8.1.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.4.5": {"type": "package", "serviceable": true, "sha512": "sha512-DArhXxeawjiGbMsYdc0oUvZIjDzcLqnwz+Us8A+wJhrOiz3EqCO8aFoYun2GokyaW2hipEJ33oxqnhU6UmaEJQ==", "path": "system.linq.dynamic.core/1.4.5", "hashPath": "system.linq.dynamic.core.1.4.5.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-qAo4jyXtC9i71iElngX7P2r+zLaiHzxKwf66sc3X91tL5Ks6fnQ1vxL04o7ZSm3sYfLExySL7GN8aTpNYpU1qw==", "path": "system.reflection.emit/4.6.0", "hashPath": "system.reflection.emit.4.6.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-j/V5HVvxvBQ7uubYD0PptQW2KGsi1Pc2kZ9yfwLixv3ADdjL/4M78KyC5e+ymW612DY8ZE4PFoZmWpoNmN2mqg==", "path": "system.reflection.emit.lightweight/4.6.0", "hashPath": "system.reflection.emit.lightweight.4.6.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "path": "system.reflection.metadata/7.0.0", "hashPath": "system.reflection.metadata.7.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "path": "system.text.encodings.web/9.0.0", "hashPath": "system.text.encodings.web.9.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-+MvhNtcvIbqmhANyKu91jQnvIRVSTiaOiFNfKWwXGHG48YAb4I/TyH8spsySiPYla7gKal5ZnF3teJqZAximyQ==", "path": "system.threading.tasks.extensions/4.5.3", "hashPath": "system.threading.tasks.extensions.4.5.3.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "TimeZoneConverter/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "path": "timezoneconverter/6.1.0", "hashPath": "timezoneconverter.6.1.0.nupkg.sha512"}, "Volo.Abp.Account.Application/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-P0Scej2ZLIIwg2KjQDiHjVutt/XJntGDcf5iSncKHP83OwwASaXFnpgtLHXRWDgaINAogCqEQRJv8JcvVfgJ/g==", "path": "volo.abp.account.application/9.0.1", "hashPath": "volo.abp.account.application.9.0.1.nupkg.sha512"}, "Volo.Abp.Account.Application.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IYhL0F7Oikon40vH+NU2w7l7dU/pNpkH09wmxFef6dFY0sk1zqTcDxzri2mJH0ZK7qRyBgpal0/BVugY6Yct1A==", "path": "volo.abp.account.application.contracts/9.0.1", "hashPath": "volo.abp.account.application.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.Account.HttpApi/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jmghhhCIDv2vDY6ejPZ3aY6eZw11b5BSYy11yTdOmha8LYH5zKJTEWVW1/Ornwedta/DGTeWPgrl/v+Qi0+T3g==", "path": "volo.abp.account.httpapi/9.0.1", "hashPath": "volo.abp.account.httpapi.9.0.1.nupkg.sha512"}, "Volo.Abp.Account.HttpApi.Client/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-mdNPCJM26JrGhCCvczS+JyqtJWCScblECbqQ8a+n5TC+5oiKjEIKSdgJG9Jke37cL+z+TLzBaoBMLc/W331O9A==", "path": "volo.abp.account.httpapi.client/9.0.1", "hashPath": "volo.abp.account.httpapi.client.9.0.1.nupkg.sha512"}, "Volo.Abp.Account.Web/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ap4y7syiFdxMOU3fXJsF/2rwcy+t5Gp71jfK3wcouBz3Aa5sjet85lkgiZFps7hMENJj0SNs80vp7FhRVg39GA==", "path": "volo.abp.account.web/9.0.1", "hashPath": "volo.abp.account.web.9.0.1.nupkg.sha512"}, "Volo.Abp.Account.Web.OpenIddict/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/6Xk40L4bMvKq7mdyZ8nc5b45rUAPXb3UYQR1Ni2WGWFEK5s7tMXZYMWHNbE12ozCXZWF5u9atCrx+IMePgg4A==", "path": "volo.abp.account.web.openiddict/9.0.1", "hashPath": "volo.abp.account.web.openiddict.9.0.1.nupkg.sha512"}, "Volo.Abp.ApiVersioning.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-q1RwxhCQC/K0ziUYPhjVkHFa09MFIht+K+Sv7xh0zGx+m7gg9WsBUOiBw2b1qxiHQyE434QIBhNUnkEAKcpkpA==", "path": "volo.abp.apiversioning.abstractions/9.0.1", "hashPath": "volo.abp.apiversioning.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IztucKXylX40p5f/40WzSijl7zgfUMwxGM0Vitwk/+LazZ1br/o9bG1CPwB0WweN3L+9TTSxxborUa6KNM8FfQ==", "path": "volo.abp.aspnetcore/9.0.1", "hashPath": "volo.abp.aspnetcore.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/SPK0yZLXxn3ijQT+p/1mQLTRFk+NB5p5qauifJBaSeTLI9bUxOi2tgatMwMkn81+9NQcS7ZzyzQSSj1xvkPPg==", "path": "volo.abp.aspnetcore.abstractions/9.0.1", "hashPath": "volo.abp.aspnetcore.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Components/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9+6AkMBACMtwBr35StMArCLK+UabFurcc6jluk5bg3n74fD+vNZaQh674aMwj7BrGhCwvUV+X8mPylI1BpR97Q==", "path": "volo.abp.aspnetcore.components/9.0.1", "hashPath": "volo.abp.aspnetcore.components.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Components.Web/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dYjY9IBZNzxjdS3ZgnIyL4xfseHjQDN/6thoNk/GvWpbaAok4T21lrAKbjASJ4BK3+cmHr9yb2WvtWYe4E5Rxw==", "path": "volo.abp.aspnetcore.components.web/9.0.1", "hashPath": "volo.abp.aspnetcore.components.web.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Components.Web.LeptonXLiteTheme/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-g//9S0vOPLab2neXfY4EE9ZBTOq575Y6AvyCeevhQNagNuaOSKn/HXabtrzpsKqZYsez2sJQE5B4Neq82n5qQA==", "path": "volo.abp.aspnetcore.components.web.leptonxlitetheme/4.0.1", "hashPath": "volo.abp.aspnetcore.components.web.leptonxlitetheme.4.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Components.Web.Theming/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jrR1n8tpru0ERMYBIQc5qexZe2oi29ZDVbdmrCrgjyPpALAA/yhqzl9VOZetBTkMTxN4dGM9F/EdB+bUX2BEqA==", "path": "volo.abp.aspnetcore.components.web.theming/9.0.1", "hashPath": "volo.abp.aspnetcore.components.web.theming.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Components.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-zN/05w1xTP/Ur4RRDbjthjjsioZjaw272s8ZqpshAJ3cQzcNJXAgZKAu4peJdoWIEOV3yQENqnN6sYVCJjrRmw==", "path": "volo.abp.aspnetcore.components.webassembly/9.0.1", "hashPath": "volo.abp.aspnetcore.components.webassembly.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXLiteTheme/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-NBlICuYvv9R6esHhLMzbambHL1NISrdyY8H7PsqMbhDxtiOVuTmtxQ5iC8NCV581bomYC9LP1pEBCejVlq9gVA==", "path": "volo.abp.aspnetcore.components.webassembly.leptonxlitetheme/4.0.1", "hashPath": "volo.abp.aspnetcore.components.webassembly.leptonxlitetheme.4.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Components.WebAssembly.Theming/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XJOywOrTFzVeWHoJmBmg0MeMc3Y/2By5aVfMDcH/UDVNfxTgWTK3PNVbJJ12nyu4/3tJGge26kC20phtWrPkVQ==", "path": "volo.abp.aspnetcore.components.webassembly.theming/9.0.1", "hashPath": "volo.abp.aspnetcore.components.webassembly.theming.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.MultiTenancy/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2HnFkW+f9+jMlWmrhaoUktiMbT5zAKME747q5xptHRkODFprM2cnjvU7u63i3emMf03lXVnqbVOsIC3onbBDiQ==", "path": "volo.abp.aspnetcore.multitenancy/9.0.1", "hashPath": "volo.abp.aspnetcore.multitenancy.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-gRciGzdX/PMvaGU7Zs/ksvmOZepfgEvJd++B3LfUUHVf+vqDagt4wBdfJmI9K0Gw8T7ijbZ3WBssVv+1gFWCTA==", "path": "volo.abp.aspnetcore.mvc/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.Client.Common/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-lKCDKzuSJJPapTn3Eg5SnKgVBULd3F4hyGUOgDTW90wF5GKdLUF0qBUKrEpG4YFC4emDNHM6A4n7MNbz7T/KzQ==", "path": "volo.abp.aspnetcore.mvc.client.common/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.client.common.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uUkTQDKpUViugZcx/euh2itZSNzNb+Vc0ydcBj2E0eKL6fbqzqpq8pYTB56K5JHGanLdwGDXJwHGv4RjQp7C5A==", "path": "volo.abp.aspnetcore.mvc.contracts/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ttOA4fgA00JFwQPx2lVyxU46neB2ObTf2gp2PlGpEqSbHfRbi4hFQ/laKf+Hr1yUo82gCffGsUYOGEgrjn1LNQ==", "path": "volo.abp.aspnetcore.mvc.ui/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bootstrap/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XZ0vNWKp+s8Mk/dBubmcPUIXsu+kgNk8NLygWnWWRauQ8SYc6bHkNpj5XuWs9qRgdeZNvE0N6njgPiac4QLgXA==", "path": "volo.abp.aspnetcore.mvc.ui.bootstrap/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.bootstrap.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FBM/xtPzHp2OBB0xgFGghLM0xZTREXY4aHIrW6wwYKPR8EucsNB6DtkYsE3crg680SwOGsrSSjxrwhf0/br6dg==", "path": "volo.abp.aspnetcore.mvc.ui.bundling/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.bundling.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Bundling.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-JHXidE86fnDlINYNKvwUPZEKbMzhraSkG63mP/izwoy5q48y0SurNq6f+GZAlrbMg1XMmTV2E9x5UZbXHYjhDQ==", "path": "volo.abp.aspnetcore.mvc.ui.bundling.abstractions/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.bundling.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.MultiTenancy/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+7/JzCAMrGHDPo2vPFUmLf5B041Mk6JKnqj2rgsXg8Svp0ngAyPuIqhIsMdCMauVDoXNl+WrNWpTEejbDZ14QQ==", "path": "volo.abp.aspnetcore.mvc.ui.multitenancy/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.multitenancy.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Packages/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-NodEJH/cXDhTJPg5yrELF6JuvSbpcqdFPngKl5jiPIcsv6i5IFGVXerZIhDxAu2J2qM3reOk5GIxtbH8ycK0GA==", "path": "volo.abp.aspnetcore.mvc.ui.packages/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.packages.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-tHLz49w5qAnsfow1kcD5UI79S7opL5XGHT283LFizL900pXnHbE2IcYeBqxsCKUmbUZ+Ddxjpj4WsTyp29xWlQ==", "path": "volo.abp.aspnetcore.mvc.ui.theme.leptonxlite/4.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.theme.leptonxlite.4.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-cnRw+pe6mc3VIAxerWOXa4qNeAsxChohsY1b2UUmFpIMJtXjzP46XIAVSbfoZVosmrN4hyGrAxNrTxC2bRukfQ==", "path": "volo.abp.aspnetcore.mvc.ui.theme.shared/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.theme.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Mvc.UI.Widgets/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OPqjnwANemBj+NCwpCYYjcTnRXNgbTaX0C02ksxxa7/ApTd39Tr1YBEoCMMwUWZj/fcrgsKs01PO2F7+/MCY/w==", "path": "volo.abp.aspnetcore.mvc.ui.widgets/9.0.1", "hashPath": "volo.abp.aspnetcore.mvc.ui.widgets.9.0.1.nupkg.sha512"}, "Volo.Abp.AspNetCore.Serilog/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-8adiGHPnI8T/wvMo48sQQU9GfBNSKqmbIUdBYhId6JixVKuJS67zNqP4Ij9X49JZ74iOMqwCQWljR4QwU0Jhvw==", "path": "volo.abp.aspnetcore.serilog/9.0.1", "hashPath": "volo.abp.aspnetcore.serilog.9.0.1.nupkg.sha512"}, "Volo.Abp.Auditing/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Y0Is7edjWAdxvF+n6ggP5/f4EoinFeUBLiCyjYfVZEMOIZG1Y1nUm1hn2ricy7S19qbvoO5n8B/BeI2DCjyBBA==", "path": "volo.abp.auditing/9.0.1", "hashPath": "volo.abp.auditing.9.0.1.nupkg.sha512"}, "Volo.Abp.Auditing.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dYzbM+NbeTSADdptTTRjuCmR/Ru8t9ulWPfrOJBnSapFPA2rpKvVZKCTFf5z6FVyTQwtLKQEECergL8j0sowQg==", "path": "volo.abp.auditing.contracts/9.0.1", "hashPath": "volo.abp.auditing.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.AuditLogging.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-R8whdbUQJ/wSwiXc1q6pbLSke4YGqJwWTsKvaMAlFdQ4HGZr4eEbhuNXAaEuFCT6z8M4mTixOuYNJRrbrFzDFg==", "path": "volo.abp.auditlogging.domain/9.0.1", "hashPath": "volo.abp.auditlogging.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.AuditLogging.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-QVu7f7PDi9AAV/sVEArFmBquqgVnnQcDiiJjopb05JZhBXMOJrKAIcYsqzyPpUhl+0qyie74GR5tj3l3kC616Q==", "path": "volo.abp.auditlogging.domain.shared/9.0.1", "hashPath": "volo.abp.auditlogging.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.AuditLogging.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-e5MEsjX77md4LPnoIQNMfpHSGeShaE6ixv/x5/6PHA0KvJ2OnBaAp9zIA/wuvDVd6ZT25hpiRQknM4sNZ018Qw==", "path": "volo.abp.auditlogging.entityframeworkcore/9.0.1", "hashPath": "volo.abp.auditlogging.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.Authorization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-U8V4aU+2Mp43Ao/U9awYqm1cx4/iP33EKbCSQlqaz7zPjFTmCfAbJ+t3OakP2VkUnA9FHCj5Q+ah9BMnsQ6B5Q==", "path": "volo.abp.authorization/9.0.1", "hashPath": "volo.abp.authorization.9.0.1.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5ZIH2WmieD7GwQyixD+V/QMqBdDRCDEXCPVK5c5NVJzlC3QRDn/laGmEPFAV6/npRcZvrtorrR8byEWe7JmMGA==", "path": "volo.abp.authorization.abstractions/9.0.1", "hashPath": "volo.abp.authorization.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.Autofac/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-aUWoc4cl3uZJ5T1f84HeqHIHNOrhou3hDiCrDAG6LD4dMOzJ0SYno4cOJfLevOqH+a0zXiXXno22XsxYaNqEAQ==", "path": "volo.abp.autofac/9.0.1", "hashPath": "volo.abp.autofac.9.0.1.nupkg.sha512"}, "Volo.Abp.Autofac.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sHRupyn6Kd2Asim6NMPu5TveMRRGTosK1iVI/hrZrb8y9OJFZ5vQznl7ofVGnxAeN1hwwPDvgtrljU0N56nj6Q==", "path": "volo.abp.autofac.webassembly/9.0.1", "hashPath": "volo.abp.autofac.webassembly.9.0.1.nupkg.sha512"}, "Volo.Abp.AutoMapper/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OswcMN8oUPGYPpyS6uPmfNpdzt/4WJQyIEENW2/A1kgqfSXCNxIgz5sFCkOSRtBYdBSNqNiFbMNQ89dPKI4/qQ==", "path": "volo.abp.automapper/9.0.1", "hashPath": "volo.abp.automapper.9.0.1.nupkg.sha512"}, "Volo.Abp.BackgroundJobs/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/DqelYuQpnhPXbB7V0ZioPb3skqL+Wv34udIpRZN1XTEKqGyswzGnFq2c+PSgSpEOF26WbKo037QGGXYl0DCLQ==", "path": "volo.abp.backgroundjobs/9.0.1", "hashPath": "volo.abp.backgroundjobs.9.0.1.nupkg.sha512"}, "Volo.Abp.BackgroundJobs.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XZvUvL2luTswBaf7IODxQp494g/Blg5THhu2seUocTzmAtaAyp1krkamn4mUC3oCQZ5EHUQD3UVYlLjMGKneAg==", "path": "volo.abp.backgroundjobs.abstractions/9.0.1", "hashPath": "volo.abp.backgroundjobs.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.BackgroundJobs.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Yjyy/Ggc14ipEmZ3fweoPGBPXZQQ9y9LpPnd29Xo/HvejjoEBM6B29bxSeruBEX84fUlxl3umewJTr/rz4GmKA==", "path": "volo.abp.backgroundjobs.domain/9.0.1", "hashPath": "volo.abp.backgroundjobs.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.BackgroundJobs.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nfYVBhZSsNNLNuKMvVrMYIxJWJxLnUqGa7+UngIktqdn/MDMNuTxokWcMv1rleiCzFlWZHV5ciVHotC/fH5qOQ==", "path": "volo.abp.backgroundjobs.domain.shared/9.0.1", "hashPath": "volo.abp.backgroundjobs.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.BackgroundJobs.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fdEW/AXqzqP3KOc+Rfa4cc35whH/bW+UB9Qj/NxxLL6hAAaJpgZfElEpa+3mdOOYMVEEAdCUedrap2mi692RQg==", "path": "volo.abp.backgroundjobs.entityframeworkcore/9.0.1", "hashPath": "volo.abp.backgroundjobs.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.BackgroundWorkers/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mzip+n8EvlhqVNQLKbj0666mWuMY0dixkzwV3VichIEMFTWn/lxrdWuCih6l67SuQiuTbu9Alu+dN2JGVo7qWQ==", "path": "volo.abp.backgroundworkers/9.0.1", "hashPath": "volo.abp.backgroundworkers.9.0.1.nupkg.sha512"}, "Volo.Abp.BlazoriseUI/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Pe3nZRqmZTBmkOl0LRaVdJgPRdeOZ0yZ9aSXr1K0kzGTjLmm5koHyXgFxda4OhAz98Lku//I7kXI8SDS2/hqFg==", "path": "volo.abp.blazoriseui/9.0.1", "hashPath": "volo.abp.blazoriseui.9.0.1.nupkg.sha512"}, "Volo.Abp.BlobStoring/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-x5HHKeBP37Bdqj3QegPyJkxXepH8nkNireBqGmDFBMAEpbe6stucB2afNrbNEQvYOwgabvi7WEnnqjCDAoAkGw==", "path": "volo.abp.blobstoring/9.0.1", "hashPath": "volo.abp.blobstoring.9.0.1.nupkg.sha512"}, "Volo.Abp.BlobStoring.Database.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-zpRcslaBx4hj9Qe3GT3j7aGC9KhRPzRYaw3ROfF+69RFahv2wLX4PumXE+yy+fefzEbi3K+zu+TjGXXAl/6Svg==", "path": "volo.abp.blobstoring.database.domain/9.0.1", "hashPath": "volo.abp.blobstoring.database.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.BlobStoring.Database.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-b3/vBhzb1p/UddU9qWO59cTeSAth92dRgFbP2Ga6Ot0VR6ttQXUZk7CKapaGCE1L2pE0TeAdi7RgZZbWmnXT/Q==", "path": "volo.abp.blobstoring.database.domain.shared/9.0.1", "hashPath": "volo.abp.blobstoring.database.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.BlobStoring.Database.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CsV+Jyie2up3d8Mq6GHcSkP6LL6/01O0GK6tWl0IqeHsaJMWNyW76UGrkZOFdZJ1s4f5aqTo+Ao36RpAcHt9bQ==", "path": "volo.abp.blobstoring.database.entityframeworkcore/9.0.1", "hashPath": "volo.abp.blobstoring.database.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.Caching/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rl6NzFISFoYyahbMXDOUWEy6HlyV1PQGTVhq+qsTAT9XyRHKf0BLNtY5ATP+VaxQAQwIJyDYKUJ0W/tTSYLrog==", "path": "volo.abp.caching/9.0.1", "hashPath": "volo.abp.caching.9.0.1.nupkg.sha512"}, "Volo.Abp.Castle.Core/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FQVQkwSF6SIS+sCY4g7YRLvv9Qw1/D0zwFIud1gIeP7cEt3HY2XYWC2UWKPYzGmQ2hU/IH2F7Z0CAQLrNAawUg==", "path": "volo.abp.castle.core/9.0.1", "hashPath": "volo.abp.castle.core.9.0.1.nupkg.sha512"}, "Volo.Abp.Core/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OMsbNxfyYA8Soy8cXqfbUyguHyvh1oN0ps//31sJcrWCNUCQlNcBmRTyLUNpRWagVP09z5JZ3mHQoTq8IwU+cA==", "path": "volo.abp.core/9.0.1", "hashPath": "volo.abp.core.9.0.1.nupkg.sha512"}, "Volo.Abp.Data/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BQdGF+tof1idXVCu7MaxkulaX2H7oferoA1we/NPnj0JR2/3HwWys+tqIxNi5CaoZKQ6P6Hrp+FvQDB0KhV/kQ==", "path": "volo.abp.data/9.0.1", "hashPath": "volo.abp.data.9.0.1.nupkg.sha512"}, "Volo.Abp.Ddd.Application/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ev9fZEksnGlW1ZXy5a47qNB9BbOkPst9BIJgVff2hV5Rew3EsPNz/004Pbllo9Q6RB0GabRRWNtuKpI0QLHoUw==", "path": "volo.abp.ddd.application/9.0.1", "hashPath": "volo.abp.ddd.application.9.0.1.nupkg.sha512"}, "Volo.Abp.Ddd.Application.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/BwF1Y1ALK5eA5NQdwUkxqa2WPislbbrgF6g2t7cEiOLsAoKDszXku4+tp42OhRuFl1+BOaCWmybkkW408TzvQ==", "path": "volo.abp.ddd.application.contracts/9.0.1", "hashPath": "volo.abp.ddd.application.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.Ddd.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BWl2YTJdTa6Yo5WwBYs3HIm3v1GinVPZRMjfhlOSpSlQOXxiQ/sEqdU2ZJ24E0QqPTq0iJ6+U9FDAAhnySY3ug==", "path": "volo.abp.ddd.domain/9.0.1", "hashPath": "volo.abp.ddd.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.Ddd.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0/gPCz7WWoqZIQqBRwCyjEbwwDe4rdgalDDJWnLHXe4B+fNfcBUOl0YrDo8FCBQKhOcR0pGQeLi6+2NgqEyMkA==", "path": "volo.abp.ddd.domain.shared/9.0.1", "hashPath": "volo.abp.ddd.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.DistributedLocking.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vX2qWNfYS6SCy9L1+K+KRG1W7bHBLbEYKAQIOmjgmKfpYXj26bLvXwp15bchuIoGKaLHPvm0px1vHpilUoC4/Q==", "path": "volo.abp.distributedlocking.abstractions/9.0.1", "hashPath": "volo.abp.distributedlocking.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.Emailing/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hMsPih/9T3ykYdTEAMqv0svzCma/CDOVLGD+ZGrcTZwZ92k9YsAWf5yfZIxuOJfoNbbtvRqtP1xo2LFrKuMlmA==", "path": "volo.abp.emailing/9.0.1", "hashPath": "volo.abp.emailing.9.0.1.nupkg.sha512"}, "Volo.Abp.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-JnsA+xdJi/BAJ9Hxf67P76yOk9yZSy9n7IPGl7s93TPR5KGh2bMHDFVjYP4uoWseL1P1zojbTV4TA7DcpVWF9A==", "path": "volo.abp.entityframeworkcore/9.0.1", "hashPath": "volo.abp.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.EntityFrameworkCore.PostgreSql/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EeGEpakqr4XFSV1MuxTyXq0CjM6H7lMFL5h2q/wxHin9nAcusliFHxKmOYiKUxj1Tge/BNDcqZzojR852EObZg==", "path": "volo.abp.entityframeworkcore.postgresql/9.0.1", "hashPath": "volo.abp.entityframeworkcore.postgresql.9.0.1.nupkg.sha512"}, "Volo.Abp.EventBus/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-567IYCdiWGFeax1RA3unL6ku0IN6xiJQqOOmto1ihsczNvI3P1jtU0Ie4YRmXrYU1ZwDFRAW1Tv4V88XlAO6Ig==", "path": "volo.abp.eventbus/9.0.1", "hashPath": "volo.abp.eventbus.9.0.1.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rcPFPkLiLk1TushfBy0bwyNz9cONp5QTosIN26EToiD2gRwkQcDPsxdXFqS1ZPYS0W7SxNzZrUe5ausEEVersg==", "path": "volo.abp.eventbus.abstractions/9.0.1", "hashPath": "volo.abp.eventbus.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.ExceptionHandling/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-YkfFEKtOMkDEj8y1M2qkkLB1MmbF9HNctukxnw5zcb2CoqcinnY/Qo/SGRLMTiKCnvz8k6L7bD7O/BV2hlFCrg==", "path": "volo.abp.exceptionhandling/9.0.1", "hashPath": "volo.abp.exceptionhandling.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Application/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-J+8sQdm7zdIP8Xdd2lQjtCFmnWC1SDdXrifaiYT3fsaeC2GpRqsdEZPYim79gIWI5O4RlfV3ssb9szY4Gbl0rQ==", "path": "volo.abp.featuremanagement.application/9.0.1", "hashPath": "volo.abp.featuremanagement.application.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Application.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-miTsz6kz8qfTuYpMf8/hSW3/yIHomKlVCYV32GzQLmPp9MmhCzSGB9CClIex/9uIwDiIJi1LmQ7Ujyiq9j0KLQ==", "path": "volo.abp.featuremanagement.application.contracts/9.0.1", "hashPath": "volo.abp.featuremanagement.application.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Blazor/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Iz4pmSGMIfKtYedelds0cnxijH4zqT/Banm7Cu7FfLg9FgX9+sBML3EEnZjJPD/3l7zEtMs7gNHm6kjtMjxJZQ==", "path": "volo.abp.featuremanagement.blazor/9.0.1", "hashPath": "volo.abp.featuremanagement.blazor.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Blazor.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ggXLjRteI3/6Zknfny8+9/u1zV2WP54juZ8L4zpuxYhiaO/MEgAyy8JOmrxxWcu1FShegFkJhyou4TNK8URsJw==", "path": "volo.abp.featuremanagement.blazor.webassembly/9.0.1", "hashPath": "volo.abp.featuremanagement.blazor.webassembly.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qrlQLuLVA0WNNUeIK2zoB+vXBuR6FJNoU2vZBR7Fn5I+Iu3114FqO+pjroaBZSv7VjXzEC0/35747HfZmeY5UQ==", "path": "volo.abp.featuremanagement.domain/9.0.1", "hashPath": "volo.abp.featuremanagement.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Z1+EJpOpg+FXeDuNzt1T5D33WjgjBjSOT1sN8uKU3llyKNqjZfgXg/cdnGR1wpCO/NHAT0rAz7SocGhO+zUTxA==", "path": "volo.abp.featuremanagement.domain.shared/9.0.1", "hashPath": "volo.abp.featuremanagement.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9SeQYP7o1WS1lHMQRn3oCRxsGj/d4xrs9ATnj/KFHpRyYPMi5dCFREb7NRWnOWPbXB5lfJiLYEO15jsC0DNAvA==", "path": "volo.abp.featuremanagement.entityframeworkcore/9.0.1", "hashPath": "volo.abp.featuremanagement.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.HttpApi/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-A7G4EKYnQjseaLr7l6UyvNY2VWhN+feI+MRSNpmyNLExjexiPRb+JmCR1s8fPD2g4tgl856GeCrUIipcyqCxug==", "path": "volo.abp.featuremanagement.httpapi/9.0.1", "hashPath": "volo.abp.featuremanagement.httpapi.9.0.1.nupkg.sha512"}, "Volo.Abp.FeatureManagement.HttpApi.Client/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FTK11Ui0RIx1PpoJjKNnMRhZX39JQ0h8FSXCsW3CQtGfhxuMA3/Pq8zotPl70/BUonn3DGV6GqywFGwnyh3V0A==", "path": "volo.abp.featuremanagement.httpapi.client/9.0.1", "hashPath": "volo.abp.featuremanagement.httpapi.client.9.0.1.nupkg.sha512"}, "Volo.Abp.Features/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VtJhzmX4lA7AdG7ZIcD/zWwTj/K8pl1i9XrOKRrWMhHhih9fHvJNjR9DlROwuJXWl4/S7tpW710IFIhoiUHCIA==", "path": "volo.abp.features/9.0.1", "hashPath": "volo.abp.features.9.0.1.nupkg.sha512"}, "Volo.Abp.GlobalFeatures/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-D53/Bb3FjMbHgHblamSwZQB7XeguKmvHdMnWF02a8MXTFvfVtOzvkn8lewBndVvB4AEf0uGndTg5RPlpFS9B/A==", "path": "volo.abp.globalfeatures/9.0.1", "hashPath": "volo.abp.globalfeatures.9.0.1.nupkg.sha512"}, "Volo.Abp.Guids/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-euzuDZ/IgQxEn6eqvXAEt5aDVSYyhn0TRac3vRRSrLsmiltbAAqa+s6uyNH25fbHoHytC7GzV6u38cX+6bB1aA==", "path": "volo.abp.guids/9.0.1", "hashPath": "volo.abp.guids.9.0.1.nupkg.sha512"}, "Volo.Abp.Http/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-1JLCXupNGbHwWQlFU56+Szx88RdF9IXTHDE9kR/Gd7PTtAzhI/msZmdwRF4GvAC4a5PKGsYSacl66kNTpNl0AA==", "path": "volo.abp.http/9.0.1", "hashPath": "volo.abp.http.9.0.1.nupkg.sha512"}, "Volo.Abp.Http.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KbTIKiPkNOOMctmolAzVan4FVOK1hfdH7AMQBVHoJOpC2zczejuL8ltOnMUoVQgFP16KG1dMUNV5hbggM9hJIQ==", "path": "volo.abp.http.abstractions/9.0.1", "hashPath": "volo.abp.http.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.Http.Client/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vmz1LOSIosfSAr8+AZDrVGrsjjHLjMTswAkKfQ/IXzt4a/bK+opEhwOMVpGU22ERaeW3l9y/gsRi8qMPulXQSQ==", "path": "volo.abp.http.client/9.0.1", "hashPath": "volo.abp.http.client.9.0.1.nupkg.sha512"}, "Volo.Abp.Http.Client.IdentityModel/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Z9lhnvILHVRtDNebr1sVAEPlRbFIyL9B6BrzYv0b+LkfMFn5cECnTWgd1TEmOdiSXJ0SlpZ3p/shlF3yU7YBWw==", "path": "volo.abp.http.client.identitymodel/9.0.1", "hashPath": "volo.abp.http.client.identitymodel.9.0.1.nupkg.sha512"}, "Volo.Abp.Http.Client.IdentityModel.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-S4m3uUAtZkYJVXRcRc3CqSTR1IxjJBjNZVaDYrZjraj+gKVpEi3H2a5kpScZtlXDXiY11sisOnZHlRUPBKWddA==", "path": "volo.abp.http.client.identitymodel.webassembly/9.0.1", "hashPath": "volo.abp.http.client.identitymodel.webassembly.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.Application/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GsVLavfJNaoAf7rMlN40qPXtYejOLqV5DvElbAhPiKhMBcNje+eagM5GRDio4gjcVB2GOiwN9exHXcReq6S74Q==", "path": "volo.abp.identity.application/9.0.1", "hashPath": "volo.abp.identity.application.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.Application.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nqs13/mDBEJ+Sw0oBMB9u427Q+d+TFQT40P2tdtxapcHRcnJKEfJYSVhlTb2zxjAP7X3VYXxoCBfYyVzUlk/Kw==", "path": "volo.abp.identity.application.contracts/9.0.1", "hashPath": "volo.abp.identity.application.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.AspNetCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OG+d3xmQmxjyjm1Lc6NxY16zZOw5M53jUvDlk6X7Bd1Xhx/jYEzlmxGDkxigMfsgEmr9gbwK+Yk5KPioldb3Ww==", "path": "volo.abp.identity.aspnetcore/9.0.1", "hashPath": "volo.abp.identity.aspnetcore.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.Blazor/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-c57avxlqhsqTrR3XWJlGMTb1l/bdQFgokRzXMtTXsjtD6swbEsR+1YDtI7Yf6jc8fEsgOkk49lviuE8rj+pJ6A==", "path": "volo.abp.identity.blazor/9.0.1", "hashPath": "volo.abp.identity.blazor.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.Blazor.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-f9ZXQDkyPjUeegSlZQTve8B8s2cXkFsZURk6djnBohzKcV9oCr1U2tR7oBlHrTj4Xv4e87BdkWwbVfVNUDN7Jg==", "path": "volo.abp.identity.blazor.webassembly/9.0.1", "hashPath": "volo.abp.identity.blazor.webassembly.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-3<PERSON><PERSON><PERSON>acprLUNi5t8ON6tDHJAc7uT8bvWc/DqwvjbBaHl0t9rthD5MXG4/n9lO8/ljtDWjzNNb+h5AgmKzgTHAA==", "path": "volo.abp.identity.domain/9.0.1", "hashPath": "volo.abp.identity.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hIVH0PRD8/d0EQi1GwOAC/nKmVGI98TXWQB5fAZQR7sF+1oUI6KbVwPB8kxF4ttsbqaWa1SuQvkE1N2QyKZmww==", "path": "volo.abp.identity.domain.shared/9.0.1", "hashPath": "volo.abp.identity.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ne+hs4eCUncgLlNKC8OhpEZEyARvZhdR4gh9FWahC/Jg2hKaxsG3UCZScLHAAbiOxTFjWDwVVIP4xmVVWXFwZA==", "path": "volo.abp.identity.entityframeworkcore/9.0.1", "hashPath": "volo.abp.identity.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.HttpApi/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+VJ2mnVymEIZ/DpRD7wOqZmcK5x3IRQe6uzBs/NALBLoUljcI99I2KjM9yQ4K10cnM0dMSmY/7Vpwmxo0GX2Gw==", "path": "volo.abp.identity.httpapi/9.0.1", "hashPath": "volo.abp.identity.httpapi.9.0.1.nupkg.sha512"}, "Volo.Abp.Identity.HttpApi.Client/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-mWrNxUIxtBhcsgHwSYoN1nfzGqsXMd1tu1K2RPLUE9mnnRsuemlDe/YTD0O0iJsb8vpubmSOMAyRKlYPSQXi4A==", "path": "volo.abp.identity.httpapi.client/9.0.1", "hashPath": "volo.abp.identity.httpapi.client.9.0.1.nupkg.sha512"}, "Volo.Abp.IdentityModel/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bJEkuTD4I7fRSdL4BTBpH2BOWq1nzQ+YYqnxfHMKH44gFElBIVHesP+ho5gjwSjn1x2exgIUygVP2FuQGWmKWQ==", "path": "volo.abp.identitymodel/9.0.1", "hashPath": "volo.abp.identitymodel.9.0.1.nupkg.sha512"}, "Volo.Abp.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kwKkAa9ZZ9iwBn6aa9ngGBuq1HxGArOTWcKAF6AaZ05+JLgrreGnHtVl9lXIWQQgEefoQb+1MpE7+s0lPG4H3Q==", "path": "volo.abp.json/9.0.1", "hashPath": "volo.abp.json.9.0.1.nupkg.sha512"}, "Volo.Abp.Json.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-U6JvZdIXLuci3QRNSqeGKN2bPK0S8RrnR1RD8rTBqBT321YqNmDB7F23kERRin2rkjYgJKE178qnjRlVJTqhhw==", "path": "volo.abp.json.abstractions/9.0.1", "hashPath": "volo.abp.json.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.Json.SystemTextJson/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-PdNSUzukV5k3acddt+ATNH4mNiGvUuA+v8PeoMG3Wyf2nFKFqpsylJ5RQmT1g+pOEivTXBA4UHpuang12asx3w==", "path": "volo.abp.json.systemtextjson/9.0.1", "hashPath": "volo.abp.json.systemtextjson.9.0.1.nupkg.sha512"}, "Volo.Abp.Localization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-R8JhXM0kDmc6BCKA6mCp7w+tDfboyNLrgYUXvMQg2+6QGYyy6DNvIHKP0YraS4IwW4aQuRAAFGJmpnXg4NCgZA==", "path": "volo.abp.localization/9.0.1", "hashPath": "volo.abp.localization.9.0.1.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-H3G9pQgAA5XU0uuIruhsILP7ifPhJDa2rGZGcebt3utIDiu2WKRW+VCUHKlL5juemXIBUcyaJvtJpu6PzrTPWw==", "path": "volo.abp.localization.abstractions/9.0.1", "hashPath": "volo.abp.localization.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.Minify/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7QTLzz5XRYnlFB1wmKUNomiuxWNZqmeY2XcG8ErAqjaYylJqRRxxv1vtXHs/z3lzwzCcCHuD5cxsfdl51f7bRw==", "path": "volo.abp.minify/9.0.1", "hashPath": "volo.abp.minify.9.0.1.nupkg.sha512"}, "Volo.Abp.MultiTenancy/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-u8QnsG1Gw5L07jA3Uoq4mJTozDVLg7ZqYRVCealsh5ac5iktZsHXsRp0uF42Fz60+qKdODOfGriprZ17WXpntw==", "path": "volo.abp.multitenancy/9.0.1", "hashPath": "volo.abp.multitenancy.9.0.1.nupkg.sha512"}, "Volo.Abp.MultiTenancy.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-khWFgB2drR0isDlmKhRfxz35d6SlGepSJqop/UeAucpa9SUtoPqldmsJ1aXeqMzD6iwh3S65XXnA5gpzsC7f8g==", "path": "volo.abp.multitenancy.abstractions/9.0.1", "hashPath": "volo.abp.multitenancy.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.ObjectExtending/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-eIDUp1IyX6ZOwQBj4lQeTBeV1sZ5fGgL/fSWY/yfdaPLo1bF9x8+u1MxOYHBed1DsOBboXVNX54x2Rzs2m1+dA==", "path": "volo.abp.objectextending/9.0.1", "hashPath": "volo.abp.objectextending.9.0.1.nupkg.sha512"}, "Volo.Abp.ObjectMapping/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UKSBTeGjzPOzb/NYd8HiQSwY4VxHUSLdbcX24YUhrmyx5egJAp4XM6nguFRqJueDpNHgh0BCsTyEYqKmSQ07Nw==", "path": "volo.abp.objectmapping/9.0.1", "hashPath": "volo.abp.objectmapping.9.0.1.nupkg.sha512"}, "Volo.Abp.OpenIddict.AspNetCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-O4TgJhO2SdYRRKNSxuuP7hgGLAcixeotNMIob6I6dz67ZyprN3W/KbBi9MiV3j83S46njaS03JJ4QRLoMIjBtQ==", "path": "volo.abp.openiddict.aspnetcore/9.0.1", "hashPath": "volo.abp.openiddict.aspnetcore.9.0.1.nupkg.sha512"}, "Volo.Abp.OpenIddict.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9TQmjYsg6mTfeYoyWy/doMLbGHf2oEnWBTUAtoPelo9sK1+SUjd2ZnZrCdtIz6Xh7+W8LzVPrpJyydHfwxR4TQ==", "path": "volo.abp.openiddict.domain/9.0.1", "hashPath": "volo.abp.openiddict.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.OpenIddict.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Eml3DnkisAcDJvSrWAVNCOIfIqRw9cfzULYvdU4zmZCrzRAbZAVfzFByRIm+XW+sg0Y+UgMDojl3/C1QMXyeqQ==", "path": "volo.abp.openiddict.domain.shared/9.0.1", "hashPath": "volo.abp.openiddict.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.OpenIddict.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ldWILykcaIQaMj4dlMrQnCME+a/4GnvXgMI6OWsBX4Elr7ara+BYJ43ufZJM5iZxLfIFtdcDQVOt/AnCllH6uA==", "path": "volo.abp.openiddict.entityframeworkcore/9.0.1", "hashPath": "volo.abp.openiddict.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Application/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>jbD47PJi9pOKB5lU0J4kUNQTx/E/ynWKfCDexV9N0VMPWsDKlj+UYCGIi40vNJ9q9VIMNyZ81gSxXpE5yvXQ==", "path": "volo.abp.permissionmanagement.application/9.0.1", "hashPath": "volo.abp.permissionmanagement.application.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Application.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vYHsvNePYwJPaeXh1DFcVDmGnFWhOmcrqPxVn63nCU0QlvWnYbsNAdGGDSRH/a6G4/uVtCGJgQsBgai8s1rxtg==", "path": "volo.abp.permissionmanagement.application.contracts/9.0.1", "hashPath": "volo.abp.permissionmanagement.application.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Blazor/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jIMU1Td2Bln3a4fcbnIBC/NyLqOkUcJgXhoIjeqonLanMnF9TYo/f9lBGVQtS6TAAu4Z0UbyfHbrtcAWhQSn+A==", "path": "volo.abp.permissionmanagement.blazor/9.0.1", "hashPath": "volo.abp.permissionmanagement.blazor.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Blazor.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-teYhDfKbh1ow3XmooAn4xZ2DEZ/YG2ooVuWaiq5oNQ6xoUgdp986SYkN9kJsldirVb3g7uSBQOYQfv2iHvf2Xg==", "path": "volo.abp.permissionmanagement.blazor.webassembly/9.0.1", "hashPath": "volo.abp.permissionmanagement.blazor.webassembly.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xAWnliD8V/sEOKxekKj/NWP+6bGkfRWl6Y+kKW0wIS4PbnHeVRE3tfEdmdN4l2BTeEHmj5V1D/ADN3O7EqelIw==", "path": "volo.abp.permissionmanagement.domain/9.0.1", "hashPath": "volo.abp.permissionmanagement.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.Identity/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sI8iK/M4eYIWtJQ+HS1Ph53/xln+rJdgr3js/POvX4qW6vduPcJ3rAYxC0owCwW5Uoye163otJG6Ex8R53ubug==", "path": "volo.abp.permissionmanagement.domain.identity/9.0.1", "hashPath": "volo.abp.permissionmanagement.domain.identity.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.OpenIddict/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2rK5Y5XoDzYmRXh1UEeM6KAlCwhPOqUcBNICB78bFM/rVlfq2kBaUOqN4xkddYAWObmkd9bLum7+PVybfLwxcg==", "path": "volo.abp.permissionmanagement.domain.openiddict/9.0.1", "hashPath": "volo.abp.permissionmanagement.domain.openiddict.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FtA7JQcpbpPxv958UpOn+8P+Vzj4Nx6ZuOYa9xSzWIm1lAR6H1Vc70umT93E4i6qoIuWWP0UNaBp3nNBo6Tvng==", "path": "volo.abp.permissionmanagement.domain.shared/9.0.1", "hashPath": "volo.abp.permissionmanagement.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VSEUmoa0K2zyDgmxGByFxrkOsxpV7qB389d16or/WfNl6oYwa5H9TZhm0AKJxy4fmiY40AN8ncWJ9DBzzYsh0g==", "path": "volo.abp.permissionmanagement.entityframeworkcore/9.0.1", "hashPath": "volo.abp.permissionmanagement.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.HttpApi/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vZxzpe/VJwvgLHnc1kkzntaZCSTxF/jkRTjkyxqzSivWr/OjD/KSulHqWVQKtbKKY7Vty5IE66+G77DE032Smw==", "path": "volo.abp.permissionmanagement.httpapi/9.0.1", "hashPath": "volo.abp.permissionmanagement.httpapi.9.0.1.nupkg.sha512"}, "Volo.Abp.PermissionManagement.HttpApi.Client/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-3VG9wv1ud/frQBEi0QlIqzkGKhQcxs0n8sNK6U35yP2XKDs33UZmj72oEyZPjQhcJ+MPkAxjp3vLcXY1T8CVuw==", "path": "volo.abp.permissionmanagement.httpapi.client/9.0.1", "hashPath": "volo.abp.permissionmanagement.httpapi.client.9.0.1.nupkg.sha512"}, "Volo.Abp.RemoteServices/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-skoWLh7SzVY3vEvZ52FO+kkmEfWnCY4FVipRu/N/kb2tBp9eYuWyL6U8VDb/Q7iibWNtHc15wQ6KvZjHocYMBw==", "path": "volo.abp.remoteservices/9.0.1", "hashPath": "volo.abp.remoteservices.9.0.1.nupkg.sha512"}, "Volo.Abp.Security/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ptfXN3j928GJuDWNLa/YwfRFngI+0eizKun28emH6LDJqgIPRUTegoYdX4RhhLEtnghpPIlYQz+Q3/xCyoyk6g==", "path": "volo.abp.security/9.0.1", "hashPath": "volo.abp.security.9.0.1.nupkg.sha512"}, "Volo.Abp.Serialization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bYjImifbYbdN+OSaT2KsluR8L9AI6mxhx4daOh6d65+QmO1zuryj1/jUrY+PqEuk346WDjyx9tv+torlm/aYRA==", "path": "volo.abp.serialization/9.0.1", "hashPath": "volo.abp.serialization.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.Application/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-aPE8qK1eg+OQv3qIxabb/MlPjZn+945oLcR29IbBDMYPDbenjGZdI9OSc7wGb9pFY6HdDMNS3vFG7zbdki5B6g==", "path": "volo.abp.settingmanagement.application/9.0.1", "hashPath": "volo.abp.settingmanagement.application.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.Application.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qV0wxLbjOL1130G0eCV3tZ/wihTlp/q7EvIshw6nyaZHAoFjFJM0Ua3T+fTyhiw/zasDb2Q2/FQj2CLZc2NB1A==", "path": "volo.abp.settingmanagement.application.contracts/9.0.1", "hashPath": "volo.abp.settingmanagement.application.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.Blazor/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dGKRjrPVvuj7v/FoUOLYg6/ZoteRLEAiJpuOoP07GNfzykez+z5UAzEOp0oDdh242xEjnKw/d9tCXD8rLiyViQ==", "path": "volo.abp.settingmanagement.blazor/9.0.1", "hashPath": "volo.abp.settingmanagement.blazor.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.Blazor.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XrDRH+gpicd6t70mzhvcZ9agFP02waM/Q+wFu0x4/P9qHG5uxvrUTNvYq5uoYwQ5471+qLa+FOYCYu4x0zl8ew==", "path": "volo.abp.settingmanagement.blazor.webassembly/9.0.1", "hashPath": "volo.abp.settingmanagement.blazor.webassembly.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sOll1pYmSTbSyhfE9qZ8e7gGBXx9x6PJDyk25hEzQQRNYES6npChbETHY9A4BcmtRLJklnA4S6VXv+MkVhbUmg==", "path": "volo.abp.settingmanagement.domain/9.0.1", "hashPath": "volo.abp.settingmanagement.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-W0UWCptGFuaT/t3mjrI6Q/3wwQZ070jEU+4Fdm04JqoQglZWsyJzLx3X/YCpMyOsfG5nXZjCC7Swd48ok8Ah7g==", "path": "volo.abp.settingmanagement.domain.shared/9.0.1", "hashPath": "volo.abp.settingmanagement.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-zLme9Jxh9WkW2sL/f7YU0+BFvswHFRrS1VSZpA5SRFQs3ubUuxIV+57vwmlYSDzEaup6nEyH7MT32WXx/ujcAw==", "path": "volo.abp.settingmanagement.entityframeworkcore/9.0.1", "hashPath": "volo.abp.settingmanagement.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.HttpApi/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Z3feQk/RXwA2zgrXxw6M6aEt7QstcxfWeQvljZy3DRGl/wn2Dov4Pd3sKXAtsFcChGJS2eU0e7QM0pIyWxz+AA==", "path": "volo.abp.settingmanagement.httpapi/9.0.1", "hashPath": "volo.abp.settingmanagement.httpapi.9.0.1.nupkg.sha512"}, "Volo.Abp.SettingManagement.HttpApi.Client/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Am4nNDd6vUBAYfyR6hznnxKXpxOyWQb6GCnHrNX73Ppfj8Nz4oaCyJPuumvd32UnRL1GtX7UFy7uM0sCDpNFlg==", "path": "volo.abp.settingmanagement.httpapi.client/9.0.1", "hashPath": "volo.abp.settingmanagement.httpapi.client.9.0.1.nupkg.sha512"}, "Volo.Abp.Settings/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nLFgI/pBCzE1ocuvO85tNfSO7NC/fjrNZhetrvuChWRt5TKcV1aF9sMu6rehMlBFi6psyNHFXBqjboWhE5FJ3g==", "path": "volo.abp.settings/9.0.1", "hashPath": "volo.abp.settings.9.0.1.nupkg.sha512"}, "Volo.Abp.Specifications/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-3hb0pmRm4CsIjqGG24dFP6Melo2qTYpt2BxNAqQEPi5Rg3SchmcrSyuBUlDcsydRMMeMrXSMWm8AwfxLV+Nzmw==", "path": "volo.abp.specifications/9.0.1", "hashPath": "volo.abp.specifications.9.0.1.nupkg.sha512"}, "Volo.Abp.Studio.Client/0.9.15": {"type": "package", "serviceable": true, "sha512": "sha512-GhfMKeEqpBXWt50VFco6qTlxAjyxwN8Y54hAesBYP14+JSskbw3S0LVtRc8+yoaHjsOtWtDwzwN+wLYLPxDlUQ==", "path": "volo.abp.studio.client/0.9.15", "hashPath": "volo.abp.studio.client.0.9.15.nupkg.sha512"}, "Volo.Abp.Studio.Client.AspNetCore/0.9.15": {"type": "package", "serviceable": true, "sha512": "sha512-cQPOpiTNAkFZzujFJcI8Lvh3HfNlyRDjT1/AB5dpijcvkEOeq2UXFPZSmOtqTYCMGhdUhA/OcJrecLk7uAqxHw==", "path": "volo.abp.studio.client.aspnetcore/0.9.15", "hashPath": "volo.abp.studio.client.aspnetcore.0.9.15.nupkg.sha512"}, "Volo.Abp.Studio.Client.Contracts/0.9.15": {"type": "package", "serviceable": true, "sha512": "sha512-6NvkK5bsmBJ6W3s+yfa51IiGsnKoK6uR8x95LyEhjucHAP+L2OytxkEUIFDpyKAexMPvan+QJijAdTjze7AODw==", "path": "volo.abp.studio.client.contracts/0.9.15", "hashPath": "volo.abp.studio.client.contracts.0.9.15.nupkg.sha512"}, "Volo.Abp.Swashbuckle/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RXpOvs7tK1gHF2cQk+3sT9tUSzcYaLKB+sGVfhHBZs0mUM9/OXZDOztVDToRxBqeGurTnFFgwa2giJyIa4qY7A==", "path": "volo.abp.swashbuckle/9.0.1", "hashPath": "volo.abp.swashbuckle.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.Application/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6QxwOwa1/P4R3/2amaHvjF5F2XmmQH+D7VTj7sobCKT9iwevuVJN5Acm7B+IbznWKxmh1QOLoiVJyk21tI/7qA==", "path": "volo.abp.tenantmanagement.application/9.0.1", "hashPath": "volo.abp.tenantmanagement.application.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.Application.Contracts/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5Adl9HVRBNWqixCbcQFgc5kpuuZ7ce7RIe4qX9cleTyrx7uHwvtFzLo78sRJbPeeFBROOI5fAqMBsMsef/XwTA==", "path": "volo.abp.tenantmanagement.application.contracts/9.0.1", "hashPath": "volo.abp.tenantmanagement.application.contracts.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.Blazor/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bTMSckuflZs0pIUWyuxLP+s6fvRsbz/LZuhpvoSCbwYPwojtzDVU0XegPOiLLD4C8XHWw+2+F3loEbWNGwNEog==", "path": "volo.abp.tenantmanagement.blazor/9.0.1", "hashPath": "volo.abp.tenantmanagement.blazor.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.Blazor.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ilFTcqkCSYyFikcBfvVTBTI7HNjVsQsu4HoTB0zG+2a1t5ROd3X+2cKwPyKtp2lfIsSaVS3WaPSD7CsViZH9iw==", "path": "volo.abp.tenantmanagement.blazor.webassembly/9.0.1", "hashPath": "volo.abp.tenantmanagement.blazor.webassembly.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-wz+CtVXF17oc28SiTQlfBZdRxKyfHC6JRa4t93AUuAmox9Iv2pRWar/JORFgJs64W7SXp5d0zsoczPk0vr6lkQ==", "path": "volo.abp.tenantmanagement.domain/9.0.1", "hashPath": "volo.abp.tenantmanagement.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Fi19TaQadqMkCcgQA0fP+YVojgQvZWtxLpz3XfDYGoJYJFI+GembypZrWMRYESminmwFvWB92NlX3EPM2QMyPg==", "path": "volo.abp.tenantmanagement.domain.shared/9.0.1", "hashPath": "volo.abp.tenantmanagement.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-9fD87VW/86hY2dX0MqVHPaeIoxbOGt7IMZBv8ef/hommGN5Wl/FGo/J39A9EzuhQmBb+Qsbt/RN/fSry+DDhNQ==", "path": "volo.abp.tenantmanagement.entityframeworkcore/9.0.1", "hashPath": "volo.abp.tenantmanagement.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.HttpApi/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-69b<PERSON>ux4Uzk1djAJlQmSl4KkEZV0zsvQohmJiXD2zncHmn5+CTYwAV+ChPltm13s5+edpws8KW4YEWMY2qXhNeQ==", "path": "volo.abp.tenantmanagement.httpapi/9.0.1", "hashPath": "volo.abp.tenantmanagement.httpapi.9.0.1.nupkg.sha512"}, "Volo.Abp.TenantManagement.HttpApi.Client/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-JdwwPnZp3+qSiRuSwpHKY4tb7UZty4LEJ16lH0znI11yhQ+gc2OfoBp8m9xJ2hy/WSxYANhaLdSouR+pD0r+kw==", "path": "volo.abp.tenantmanagement.httpapi.client/9.0.1", "hashPath": "volo.abp.tenantmanagement.httpapi.client.9.0.1.nupkg.sha512"}, "Volo.Abp.TextTemplating/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-93H7Q1lZHaJxaHCRGzB469quvIGCho0ygrP3Kg6iU0oUfU55n7wDiSqrARHCIOm6OXfg9hfANAr/xppq3eOs6g==", "path": "volo.abp.texttemplating/9.0.1", "hashPath": "volo.abp.texttemplating.9.0.1.nupkg.sha512"}, "Volo.Abp.TextTemplating.Core/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MIvvKMITQoJhXpVEbSlpk2RrFTrMn+BEn0PXa9TWKjqctfWJB5iT1MhFyzJDQNILg41Vk0tqxviIS2m48AgRwQ==", "path": "volo.abp.texttemplating.core/9.0.1", "hashPath": "volo.abp.texttemplating.core.9.0.1.nupkg.sha512"}, "Volo.Abp.TextTemplating.Scriban/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pGcyXbOlZiZN/K+qQfHq4vqkur+PGb6Nrtniwd6ytwGVixThmhapEY3FOiTV7izkC3DRviosLpx9msgT1PIpKw==", "path": "volo.abp.texttemplating.scriban/9.0.1", "hashPath": "volo.abp.texttemplating.scriban.9.0.1.nupkg.sha512"}, "Volo.Abp.Threading/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LVJiWzYu6byJZrcQM+gipX5v53RKNHWc+zstmuBkMOotScdc2Mh2towcezpwzhDvlHMxGaJgrymvqYAadQ1qRA==", "path": "volo.abp.threading/9.0.1", "hashPath": "volo.abp.threading.9.0.1.nupkg.sha512"}, "Volo.Abp.Timing/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6OxFBHuK/EkvSQFyeA0KKefD0RLlAWQO3Ymj6UFRm15Ma+MUsTCv4me0sxPDAecAGR8yUjgyhqPwc9t2dOVpAg==", "path": "volo.abp.timing/9.0.1", "hashPath": "volo.abp.timing.9.0.1.nupkg.sha512"}, "Volo.Abp.UI/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-X+rpqrTQ2bixHmuXXTiAXW7TwGGa93x27pN3KGyvVbF5BOU2GeabNMCtcicnFPK7nmk0jJynsZZ4oFDWugi6hw==", "path": "volo.abp.ui/9.0.1", "hashPath": "volo.abp.ui.9.0.1.nupkg.sha512"}, "Volo.Abp.UI.Navigation/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0VWHq2eDD2k9nNHyMCL5+DuR5WTWBIUeh7mXknmtFD+Sxilt8EvPlTsER5HIHcxTBCerFQ7rNO6pv0B62T5Rqg==", "path": "volo.abp.ui.navigation/9.0.1", "hashPath": "volo.abp.ui.navigation.9.0.1.nupkg.sha512"}, "Volo.Abp.Uow/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-PME96UT5FMfrRqDjCqprMBJh3Ct3n7GeYpwJkBmq3SqzmdtnihtV+MFv7EYbE9CK2myL0/le+zwLrefrn2maWg==", "path": "volo.abp.uow/9.0.1", "hashPath": "volo.abp.uow.9.0.1.nupkg.sha512"}, "Volo.Abp.Users.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0I6BlHIP43yAq140/rFDFx7agJk75LvLalQ0XCVd08Y2asmqDwMyNkKBCGeTLdLmApPsuD1Reo/bIMNbeZVTYg==", "path": "volo.abp.users.abstractions/9.0.1", "hashPath": "volo.abp.users.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.Users.Domain/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-NIodsFSyUohVKP/wIK7zqk42S/05E89zxk+d5zjOCc7jkYJUM25MT9cy0PUuCE4DtjbGoeNjK5Hub18zApDNDg==", "path": "volo.abp.users.domain/9.0.1", "hashPath": "volo.abp.users.domain.9.0.1.nupkg.sha512"}, "Volo.Abp.Users.Domain.Shared/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-js44a2qrqb2yXpYPfBjVJSLbj4JGFG/IVA3DgTRP8Lw0cF/pDiIc7GnlyqorOMDKm285BjNd1kmdhDVdsB8bZw==", "path": "volo.abp.users.domain.shared/9.0.1", "hashPath": "volo.abp.users.domain.shared.9.0.1.nupkg.sha512"}, "Volo.Abp.Users.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CN9po1di3Og94XH/dmAt2pr6188GxrC5WGEcNt4jCstGeY7afu+Dgs18vbPfYIkMKw0+bkVlFELA7NNe4OiBWQ==", "path": "volo.abp.users.entityframeworkcore/9.0.1", "hashPath": "volo.abp.users.entityframeworkcore.9.0.1.nupkg.sha512"}, "Volo.Abp.Validation/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OivpVF6xMl8q0EL3AuhKjIy5NDoeo1zx/9nUE7judTlwrxt7gWaYstEPdHZ4Xm6c8jPLryJST71AGLiLiUXicg==", "path": "volo.abp.validation/9.0.1", "hashPath": "volo.abp.validation.9.0.1.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hOehX0pWP5vdiDYO3tsVvpePwc6VWZzl5SxMhLcEAGov+7PW9mhu2q4N2IYTQEcf020LMDCTTF3k2J4rI9RzfQ==", "path": "volo.abp.validation.abstractions/9.0.1", "hashPath": "volo.abp.validation.abstractions.9.0.1.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-WyVn6OxFiREVcjL5nBIQVLLimGeMeUaOYz3s0GbacT7+fdZgoEUC5U+Ffg1CKltoR/362IQO3LygDBGHavAWGg==", "path": "volo.abp.virtualfilesystem/9.0.1", "hashPath": "volo.abp.virtualfilesystem.9.0.1.nupkg.sha512"}, "YamlDotNet/15.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-qeX0XhzOIcQEvnI5JxnPaIwcINwyY4Qy/LXhSfsdHkFrl9F41AT52UFfy2nIE7kgrhMg+cP7xuS+GtPJhmHmTA==", "path": "yamldotnet/15.1.2", "hashPath": "yamldotnet.15.1.2.nupkg.sha512"}, "MboraMaxNext.Blazor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MboraMaxNext.Contracts/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Results/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpLogging/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OutputCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RequestDecompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticAssets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/1*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Tar/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.JavaScript/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.OpenSsl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}