"restore":{"projectUniqueName":"/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Host/MboraMaxNext.Host.csproj","projectName":"MboraMaxNext.Host","projectPath":"/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Host/MboraMaxNext.Host.csproj","outputPath":"/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Host/obj/","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Blazor/MboraMaxNext.Blazor.csproj":{"projectPath":"/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Blazor/MboraMaxNext.Blazor.csproj"},"/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Contracts/MboraMaxNext.Contracts.csproj":{"projectPath":"/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Contracts/MboraMaxNext.Contracts.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.200"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Bogus":{"target":"Package","version":"[35.6.1, )"},"ClosedXML":{"target":"Package","version":"[0.104.2, )"},"Humanizer.Core":{"target":"Package","version":"[3.0.0-beta.54, )"},"Microsoft.AspNetCore.Components.WebAssembly.Server":{"target":"Package","version":"[9.0.0, )"},"Microsoft.EntityFrameworkCore.Tools":{"include":"Runtime, Build, Native, ContentFiles, Analyzers","suppressParent":"Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive","target":"Package","version":"[9.0.0, )"},"Serilog.AspNetCore":{"target":"Package","version":"[8.0.2, )"},"Serilog.Sinks.Async":{"target":"Package","version":"[2.0.0, )"},"Volo.Abp.Account.Application":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.Account.HttpApi":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.Account.Web.OpenIddict":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.AspNetCore.Mvc":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite":{"target":"Package","version":"[4.0.1, )"},"Volo.Abp.AspNetCore.Serilog":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.AuditLogging.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.AutoMapper":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.Autofac":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.BackgroundJobs.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.BlobStoring.Database.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.Caching":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.EntityFrameworkCore.PostgreSql":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.FeatureManagement.Application":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.FeatureManagement.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.FeatureManagement.HttpApi":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.Identity.Application":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.Identity.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.Identity.HttpApi":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.OpenIddict.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.PermissionManagement.Application":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.PermissionManagement.Domain.Identity":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.PermissionManagement.Domain.OpenIddict":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.PermissionManagement.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.PermissionManagement.HttpApi":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.SettingManagement.Application":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.SettingManagement.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.SettingManagement.HttpApi":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.Studio.Client.AspNetCore":{"target":"Package","version":"[0.9.15, )"},"Volo.Abp.Swashbuckle":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.TenantManagement.Application":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.TenantManagement.EntityFrameworkCore":{"target":"Package","version":"[9.0.1, )"},"Volo.Abp.TenantManagement.HttpApi":{"target":"Package","version":"[9.0.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/local/share/dotnet/sdk/9.0.202/PortableRuntimeIdentifierGraph.json"}}