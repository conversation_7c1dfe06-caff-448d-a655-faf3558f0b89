is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = MboraMaxNext
build_property.RootNamespace = MboraMaxNext
build_property.ProjectDir = /Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Host/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = true
build_property.MSBuildProjectDirectory = /Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Host
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Host/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50cy9BcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[/Users/<USER>/Documents/Github/mboraMaxNext/MboraMaxNext.Host/Pages/Components/Brand/Default.cshtml]
build_metadata.AdditionalFiles.TargetPath = UGFnZXMvQ29tcG9uZW50cy9CcmFuZC9EZWZhdWx0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 
