@using Microsoft.AspNetCore.Components.Web
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>MboraMaxNext</title>
    <base href="/" />

    <!--ABP:Styles-->
    <link href="global.css?_v=638824101050934380" rel="stylesheet"/>
    <link href="main.css" rel="stylesheet"/>
    <link href="theme.css" rel="stylesheet" />
    <!--/ABP:Styles-->
    <link href="MboraMaxNext.Blazor.styles.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>

    <HeadOutlet @rendermode="new InteractiveWebAssemblyRenderMode(prerender: false)" />
</head>

<body class="abp-application-layout">

    <script src="_framework/blazor.web.js"></script>

    <div id="ApplicationContainer">
        <div class="spinner">
            <div class="double-bounce1"></div>
            <div class="double-bounce2"></div>
        </div>

        <Routes @rendermode="new InteractiveWebAssemblyRenderMode(prerender: false)"/>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <!--ABP:Scripts-->
    <script src="global.js?_v=638824101053538319"></script>
    <!--/ABP:Scripts-->

</body>

</html>