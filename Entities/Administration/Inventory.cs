using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;

namespace MboraMaxNext.Entities.Administration;

using Administration;

public class Inventory : AuditedAggregateRoot<Guid>, IMultiTenant
{
    public string InventoryName { get; set; } = string.Empty;

    public Guid HubId { get; set; }
    public Hub? Hub { get; set; }

    public Guid ProductId { get; set; }
    public Product? Product { get; set; }

    public int Quantity { get; set; }

    public int Threshold { get; set; } = 2;

    public Guid? TenantId { get; }

    protected Inventory()
    {
    }

    public Inventory(Guid? tenantId)
    {
        TenantId = tenantId;
    }
}
