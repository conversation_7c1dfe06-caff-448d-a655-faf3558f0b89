using Volo.Abp.Domain.Entities;
using Volo.Abp.MultiTenancy;

namespace MboraMaxNext.Entities.Administration.Hubs;

public class AgentHub : Entity<Guid>, IMultiTenant
{
    public Guid AgentId { get; set; }
    public Agent Agent { get; set; } = default!;
        
    public Guid HubId { get; set; } 
    public Hub Hub { get; set; } = default!;
        
    public Guid? TenantId { get; set; }

    protected AgentHub()
    {
    }

    public AgentHub(Guid? tenantId = null)
    {
        TenantId = tenantId;
    }
   
}