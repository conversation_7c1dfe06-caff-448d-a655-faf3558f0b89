using System.ComponentModel;

namespace MboraMaxNext.Entities.Locations;

public enum District
{
    // Southern Region
    [Description("Balaka")]
    Balaka = 1,

    [Description("Blantyre")]
    Blantyre = 2,

    [Description("Chikwawa")]
    Chikwawa = 3,

    [Description("Chiradzulu")]
    Chiradzulu = 4,

    [Description("Machinga")]
    Machinga = 5,

    [Description("Mangochi")]
    Mangochi = 6,

    [Description("Mulanje")]
    Mulanje = 7,

    [Description("Mwanza")]
    Mwanza = 8,

    [Description("Nsanje")]
    Nsanje = 9,

    [Description("Thyolo")]
    T<PERSON>olo = 10,

    [Description("Phalombe")]
    Phalombe = 11,

    [Description("Zomba")]
    Zomba = 12,

    [Description("Neno")]
    Neno = 13,

    // Central Region
    [Description("Dedza")]
    Dedza = 14,

    [Description("Dowa")]
    Dowa = 15,

    [Description("Kasungu")]
    Kasungu = 16,

    [Description("Lilongwe")]
    Lilongwe = 17,

    [Description("Mchinji")]
    Mchinji = 18,

    [Description("Nkhotakota")]
    Nkhotakota = 19,

    [Description("Ntcheu")]
    Ntcheu = 20,

    [Description("Ntchisi")]
    Ntchisi = 21,

    [Description("Salima")]
    Salima = 22,

    // Northern Region
    [Description("Chitipa")]
    Chitipa = 23,

    [Description("Karonga")]
    Karonga = 24,

    [Description("Likoma")]
    Likoma = 25,

    [Description("Mzimba")]
    Mzimba = 26,

    [Description("Nkhata Bay")]
    NkhataBay = 27,

    [Description("Rumphi")]
    Rumphi = 28
}