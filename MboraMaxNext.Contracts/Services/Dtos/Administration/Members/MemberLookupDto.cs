using MboraMaxNext.Entities.Administration;
using Volo.Abp.Application.Dtos;

namespace MboraMaxNext.Services.Dtos.Administration.Members
{
    public class MemberLookupDto : AuditedEntityDto<Guid>
    {
        public string UserFullName { get; set; } = string.Empty;
        public long MboraId { get; set; }
        public bool Temporary { get; set; }
        public string VillageName { get; set; } = string.Empty;
        public bool IsLoanOngoing { get; set; }
        public MemberStatus Status { get; set; }
        public string Initials => string.Join("", UserFullName.Split().Select(n => n[0])).ToUpper();
    }
}