using Volo.Abp.Application.Dtos;

namespace MboraMaxNext.Services.Dtos.Administration.Hubs;

public class HubDto : AuditedEntityDto<Guid>
{
    public string HubCode { get; set; } = string.Empty;
    public string HubName { get; set; } = string.Empty;
    public Guid HubVillageId { get; set; }
    public string HubVillage { get; set; } = string.Empty;
    public string HubTraditionalAuthority { get; set; } = string.Empty;
    public string HubAddress { get; set; } = string.Empty;
    public string HubEmail { get; set; } = string.Empty;
    public Guid? HubManagerId { get; set; }
    public string? HubManager { get; set; }
    public string? HubTelephone { get; set; }
}