using Volo.Abp.Application.Dtos;

namespace MboraMaxNext.Services.Dtos.Administration.VisitorLogs;

public class GetVisitorLogInput : PagedAndSortedResultRequestDto
{
    public string? Filter { get; set; }
    public bool FilterByHubs { get; set; }
    public List<Guid>? HubIds { get; set; }
    public bool FilterByServices { get; set; }
    public List<Guid>? ServiceIds { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}