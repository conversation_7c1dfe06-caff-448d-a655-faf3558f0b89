namespace MboraMaxNext.Services.Dtos.Administration.VisitorLogs;

public class VisitorLogStatisticsDto
{
    public int TotalVisits { get; set; }
    public int MaleCount { get; set; }
    public int FemaleCount { get; set; }
    public IEnumerable<TimelineDataPoint> MaleTimeline { get; set; } = new List<TimelineDataPoint>();
    public IEnumerable<TimelineDataPoint> FemaleTimeline { get; set; } = new List<TimelineDataPoint>();
    public HubStatisticsDto? HighTrafficHub { get; set; }
    public ServiceStatisticsDto? PopularService { get; set; }
    public VisitDurationStatisticsDto? VisitDuration { get; set; }
    
    public string? DurationLabel {get; set;}
}

public class TimelineDataPoint
{
    public DateTime Date { get; set; }
    public int Count { get; set; }
}
public class HubStatisticsDto
{
    public Guid HubId { get; set; }
    public string HubName { get; set; }
    public int VisitCount { get; set; }
    public double PercentageChange { get; set; }
}

public class ServiceStatisticsDto
{
    public Guid ServiceId { get; set; }
    public string ServiceName { get; set; }
    public int VisitCount { get; set; }
    public double PercentageOfTotal { get; set; }
}

public class VisitDurationStatisticsDto
{
    public TimeSpan AverageDurationMinutes { get; set; }
    public TimeSpan PeriodAverageDurationMinutes { get; set; }
    public int PeriodChangeInMinutes { get; set; }
}
