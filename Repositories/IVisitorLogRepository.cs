using MboraMaxNext.Entities.Administration;
using MboraMaxNext.Services.Dtos.Administration.VisitorLogs;
using Volo.Abp.Domain.Repositories;

namespace MboraMaxNext.Repositories;

public interface IVisitorLogRepository : IRepository<VisitorLog, Guid>
{
    Task<IQueryable<VisitorLog>> GetFilteredQueryAsync(GetVisitorLogInput input, bool filterByDate = true);
    IQueryable<VisitorLog> ApplySorting(IQueryable<VisitorLog> query, string inputSorting);
    Task<VisitorLogStatisticsDto> GetVisitorLogStatisticsAsync(GetVisitorLogInput? input = null);
}