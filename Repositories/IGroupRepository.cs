using MboraMaxNext.Entities.Administration.Hubs;
using MboraMaxNext.Services.Dtos.Administration.Groups;
using MboraMaxNext.Services.Dtos.Administration.Hubs;
using Volo.Abp.Domain.Repositories;

namespace MboraMaxNext.Repositories
{
    public interface IGroupRepository : IRepository<Group, Guid>
    {
        IQueryable<GroupWithCount> ApplySorting(IQueryable<GroupWithCount> query, string sorting);
        Task<GroupStatisticsDto> GetGroupStatisticsAsync(GetGroupsInput? input = null);
        Task<IQueryable<Group>> GetFilteredQueryAsync(GetGroupsInput input);
    }

    public class GroupWithCount
    {
        public Group Group { get; set; }
        public int MemberCount { get; set; }
    }
}