using System.Linq.Dynamic.Core;
using AutoMapper;
using ClosedXML.Excel;
using MboraMaxNext.Entities.Administration;
using MboraMaxNext.Entities.Locations;
using MboraMaxNext.Permissions;
using MboraMaxNext.Services.Dtos.Administration.Hubs;
using MboraMaxNext.Services.Dtos.Administration.Villages;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Validation;

namespace MboraMaxNext.Services.Administration;

[Authorize(MboraMaxNextPermissions.Hubs.Default)]
public class HubAppService :
    CrudAppService<
        Hub,
        HubDto,
        Guid,
        GetHubsInput,
        CreateUpdateHubDto>,
    IHubAppService
{
    private readonly IRepository<Hub, Guid> _hubRepository;
    private readonly IRepository<Village, Guid> _villageRepository;
    private readonly IIdentityUserRepository _userRepository;

    public HubAppService(IRepository<Hub, Guid> repository, IRepository<Village, Guid> villageRepository,
        IIdentityUserRepository userRepository) :
        base(repository)
    {
        _hubRepository = repository;
        _villageRepository = villageRepository;
        _userRepository = userRepository;

        GetPolicyName = MboraMaxNextPermissions.Hubs.Default;
        GetListPolicyName = MboraMaxNextPermissions.Hubs.Default;
        CreatePolicyName = MboraMaxNextPermissions.Hubs.Create;
        UpdatePolicyName = MboraMaxNextPermissions.Hubs.Edit;
        DeletePolicyName = MboraMaxNextPermissions.Hubs.Delete;
    }
    
    public override async Task<HubDto> CreateAsync(CreateUpdateHubDto input)
    {

        // Create hub with tenant first
        var hub = new Hub(CurrentTenant.Id);
    
        // Then let AutoMapper map the rest of the properties
        ObjectMapper.Map(input, hub);
    
        // Set generated code
        hub.HubCode = await GenerateNextHubCodeAsync();

        await Repository.InsertAsync(hub);

        return ObjectMapper.Map<Hub, HubDto>(hub);
    }

    public override async Task<HubDto> GetAsync(Guid id)
    {
        var hubQueryable = await _hubRepository.GetQueryableAsync();

        var queryResult = hubQueryable.Where(hub => hub.Id == id)
            .Include(h => h.Village)
            .Include(h => h.Manager)
            .FirstOrDefault();

        if (queryResult == null)
        {
            throw new EntityNotFoundException(entityType: typeof(Hub), id: id);
        }

        var hubDto = ObjectMapper.Map<Hub, HubDto>(queryResult);
        hubDto.HubVillage = queryResult.Village?.VillageName ?? "No Village";

        if (queryResult.Manager is null) return hubDto;
        hubDto.HubManager = queryResult.Manager.UserName;

        return hubDto;
    }

    public override async Task<PagedResultDto<HubDto>> GetListAsync(GetHubsInput input)
    {
        var hubQueryable = await _hubRepository.GetQueryableAsync();

        // Apply text filter
        var query = hubQueryable
            .Include(h => h.Village)
            .Include(h => h.Manager)
            .WhereIf(
                !input.Filter.IsNullOrWhiteSpace(),
                x => x.HubCode.ToUpper().Contains(input.Filter.ToUpper()) ||
                     x.HubName.ToUpper().Contains(input.Filter.ToUpper()) ||
                     (x.Village != null && x.Village.VillageName.ToUpper().Contains(input.Filter.ToUpper()))
            );

        // Apply district filter if enabled
        if (input.FilterByDistricts)
        {
            // If filtering by districts is enabled but no districts are selected, return empty result
            if (input.Districts == null || input.Districts.Count == 0)
            {
                return new PagedResultDto<HubDto>(0, new List<HubDto>());
            }

            query = query.Where(h => h.Village != null && input.Districts.Contains(h.Village.VillageDistrict));
        }

        // Get total count
        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            // Handle special cases for related properties
            if (input.Sorting.Contains("HubManager", StringComparison.OrdinalIgnoreCase))
            {
                var isDescending = input.Sorting.EndsWith(" DESC", StringComparison.OrdinalIgnoreCase);
                query = isDescending
                    ? query.OrderByDescending(h => h.Manager.UserName)
                    : query.OrderBy(h => h.Manager.UserName);
            }
            else if (input.Sorting.Contains("HubVillage", StringComparison.OrdinalIgnoreCase))
            {
                var isDescending = input.Sorting.EndsWith(" DESC", StringComparison.OrdinalIgnoreCase);
                query = isDescending
                    ? query.OrderByDescending(h => h.Village.VillageName)
                    : query.OrderBy(h => h.Village.VillageName);
            }
            else
            {
                query = query.OrderBy(input.Sorting);
            }
        }
        else
        {
            query = query.OrderBy(h => h.HubName);
        }

        // Apply pagination
        query = query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount);

        // Execute query and map to DTOs
        var hubs = await AsyncExecuter.ToListAsync(query);
        var hubDtos = new List<HubDto>();

        foreach (var item in hubs)
        {
            var hubDto = ObjectMapper.Map<Hub, HubDto>(item);
            hubDto.HubVillage = item.Village?.VillageName ?? "No Village";
            if (item.Manager is not null) hubDto.HubManager = item.Manager.UserName;
            hubDtos.Add(hubDto);
        }

        return new PagedResultDto<HubDto>(totalCount, hubDtos);
    }

    public async Task<HubDto> GetUserHubAsync(Guid userId)
    {
        var userHub = await Repository.FirstOrDefaultAsync(h => h.ManagerId == userId);
        if (userHub == null)
        {
            return null;
        }

        return ObjectMapper.Map<Hub, HubDto>(userHub);
    }

    private async Task<string> GenerateNextHubCodeAsync()
    {
        var hubQueryable = await _hubRepository.GetQueryableAsync();
        var lastHub = await hubQueryable.OrderByDescending(x => x.HubCode)
            .FirstOrDefaultAsync();

        if (lastHub == null)
        {
            return "HUB01";
        }

        // Extract number part after "HUB"
        if (lastHub.HubCode.StartsWith("HUB") && 
            int.TryParse(lastHub.HubCode[3..], out var lastNumber))
        {
            return $"HUB{(lastNumber + 1):D3}";
        }

        throw new AbpValidationException("Invalid hub code format in database");
    }

    public async Task<ListResultDto<VillageLookupDto>> GetVillageLookupAsync()
    {
        var villages = await _villageRepository.GetListAsync();

        return new ListResultDto<VillageLookupDto>(
            ObjectMapper.Map<List<Village>, List<VillageLookupDto>>(villages)
        );
    }

    public async Task<ListResultDto<UserLookupDto>> GetManagerLookupAsync()
    {
        var managers = await _userRepository.GetListByNormalizedRoleNameAsync("MANAGER");

        var activeManagers = managers.Where(man => man.IsActive).ToList();

        return new ListResultDto<UserLookupDto>(
            ObjectMapper.Map<List<IdentityUser>, List<UserLookupDto>>(activeManagers)
        );
    }

    public async Task<ListResultDto<District>> GetDistrictsLookupAsync()
    {
        var villageQueryable = await _villageRepository.GetQueryableAsync();
        var query = villageQueryable
            .Select(v => v.VillageDistrict)
            .Distinct()
            .OrderBy(d => d);

        var districts = await AsyncExecuter.ToListAsync(query);
        return new ListResultDto<District>(districts);
    }

    public async Task<byte[]> ExportToExcelAsync(GetHubsInput input)
    {
        var hubQueryable = await _hubRepository.GetQueryableAsync();

        // Apply text filter
        var query = hubQueryable
            .Include(h => h.Village)
            .Include(h => h.Manager)
            .WhereIf(
                !input.Filter.IsNullOrWhiteSpace(),
                x => x.HubCode.ToUpper().Contains(input.Filter.ToUpper()) ||
                     x.HubName.ToUpper().Contains(input.Filter.ToUpper()) ||
                     (x.Village != null && x.Village.VillageName.ToUpper().Contains(input.Filter.ToUpper()))
            );

        // Apply district filter if enabled
        if (input.FilterByDistricts)
        {
            // If filtering by districts is enabled but no districts are selected, return empty result
            if (input.Districts == null || input.Districts.Count == 0)
            {
                return Array.Empty<byte>();
            }

            query = query.Where(h => h.Village != null && input.Districts.Contains(h.Village.VillageDistrict));
        }

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            // Handle special cases for related properties
            if (input.Sorting.Contains("HubManager", StringComparison.OrdinalIgnoreCase))
            {
                var isDescending = input.Sorting.EndsWith(" DESC", StringComparison.OrdinalIgnoreCase);
                query = isDescending
                    ? query.OrderByDescending(h => h.Manager.UserName)
                    : query.OrderBy(h => h.Manager.UserName);
            }
            else if (input.Sorting.Contains("HubVillage", StringComparison.OrdinalIgnoreCase))
            {
                var isDescending = input.Sorting.EndsWith(" DESC", StringComparison.OrdinalIgnoreCase);
                query = isDescending
                    ? query.OrderByDescending(h => h.Village.VillageName)
                    : query.OrderBy(h => h.Village.VillageName);
            }
            else
            {
                query = query.OrderBy(input.Sorting);
            }
        }
        else
        {
            query = query.OrderBy(h => h.HubName);
        }

        var hubs = await AsyncExecuter.ToListAsync(query);
        var hubDtos = new List<HubDto>();

        foreach (var item in hubs)
        {
            var hubDto = ObjectMapper.Map<Hub, HubDto>(item);
            hubDto.HubVillage = item.Village?.VillageName ?? "No Village";
            if (item.Manager is not null) hubDto.HubManager = item.Manager.UserName;
            hubDtos.Add(hubDto);
        }

        if (!hubDtos.Any())
        {
            return Array.Empty<byte>();
        }

        using var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add("Hubs");

        // Add headers
        worksheet.Cell(1, 1).Value = "Hub Code";
        worksheet.Cell(1, 2).Value = "Hub Name";
        worksheet.Cell(1, 3).Value = "Village";
        worksheet.Cell(1, 4).Value = "District";
        worksheet.Cell(1, 5).Value = "Manager";

        // Style headers
        var headerRange = worksheet.Range(1, 1, 1, 5);
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Fill.BackgroundColor = XLColor.LightGray;

        // Get villages for district lookup
        var villageIds = hubDtos.Select(hd => hd.HubVillageId).Distinct().ToList();
        var villages = await _villageRepository.GetListAsync(v => villageIds.Contains(v.Id));
        var villageDistrictMap = villages.ToDictionary(v => v.Id, v => v.VillageDistrict);

        // Add data
        var row = 2;
        foreach (var hub in hubDtos)
        {
            var district = villageDistrictMap.TryGetValue(hub.HubVillageId, out var d)
                ? d.ToString()
                : "No District";

            worksheet.Cell(row, 1).Value = hub.HubCode;
            worksheet.Cell(row, 2).Value = hub.HubName;
            worksheet.Cell(row, 3).Value = hub.HubVillage;
            worksheet.Cell(row, 4).Value = district;
            worksheet.Cell(row, 5).Value = hub.HubManager;
            row++;
        }

        // Auto-fit columns
        worksheet.Columns().AdjustToContents();

        // Add borders
        var dataRange = worksheet.Range(1, 1, row - 1, 5);
        dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
        dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

        using var stream = new MemoryStream();
        workbook.SaveAs(stream);
        return stream.ToArray();
    }
}

