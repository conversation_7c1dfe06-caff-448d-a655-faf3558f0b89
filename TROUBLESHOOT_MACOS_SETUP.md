# ABP.io MacOS Setup Troubleshooting Guide

## Prerequisites

### Download .NET SDK
1. Visit the official .NET download page: https://dotnet.microsoft.com/en-us/download
2. For M1/M2 Macs (Apple Silicon): Download the ARM64 .dmg installer
3. For Intel Macs: Download the x64 .dmg installer

### Available .NET Versions
- .NET 8.0 (Latest LTS): https://dotnet.microsoft.com/en-us/download/dotnet/8.0
- .NET 7.0: https://dotnet.microsoft.com/en-us/download/dotnet/7.0
- .NET 6.0 (LTS): https://dotnet.microsoft.com/en-us/download/dotnet/6.0

## Common Issues and Solutions

### 1. .NET SDK Not Found or Version Mismatch

#### Symptoms
- Error messages indicating missing .NET runtime
- Architecture-specific errors (arm64)
- Framework version mismatches

#### Solutions

1. Install the correct .NET SDK version:
```bash
brew install dotnet-sdk
```
> Note: Alternatively, download and install the .dmg file from the links above.

2. Verify installation:
```bash
dotnet --list-sdks
dotnet --list-runtimes
```

3. Set PATH in shell profile (~/.zshrc or ~/.bash_profile):
```bash
export DOTNET_ROOT=/usr/local/share/dotnet
export PATH=$PATH:$DOTNET_ROOT
```

### 2. ABP CLI Installation Issues

#### Symptoms
- "Old CLI installation failed"
- Process start failures
- File permission issues

#### Solutions

1. Clean existing ABP CLI installations:
```bash
dotnet tool uninstall -g Volo.Abp.Cli
```

2. Install the latest ABP CLI globally:
```bash
dotnet tool install -g Volo.Abp.Cli
```

3. Fix permission issues:
```bash
sudo chown -R $USER ~/.abp
chmod -R 755 ~/.abp
```

### 3. Bundle Command Failures

#### Symptoms
- "Failed to start a process with file path '/Users/<USER>/.abp/studio/cli/old/abp-old'"
- Working directory doesn't exist errors

#### Solutions

##### A. Clean the ABP Studio Directory

```bash
# Note: The .abp folder is hidden by default
rm -rf ~/.abp/studio
```

###### Accessing Hidden Files:

**In Finder:**
- Press `Command + Shift + .` (dot) to toggle hidden files
- The `.abp` folder will become visible in your home directory
- Or use `Command + Shift + G` and type `~/.abp`

**In Terminal:**
- Commands work regardless of file visibility
- Use `ls -la ~/.abp` to view hidden directory contents

##### B. Reinstall ABP CLI and Dependencies
```bash
dotnet tool uninstall -g Volo.Abp.Cli
dotnet tool install -g Volo.Abp.Cli
abp install-libs
```

## Project Setup Steps

### 1. Create New Project
```bash
abp new YourProjectName
```

### 2. Install Dependencies
```bash
abp install-libs
```

### 3. Update Database ⚠️
```bash
dotnet run --migratedatabase
```

### 4. Bundle Assets
```bash
abp bundle
```

## Additional Tips

- Always run `dotnet restore` after package reference changes
- Ensure you're in the correct project directory
- Use `dotnet clean` followed by `dotnet build` for build issues
- Check file permissions for access denied errors

## Verification

### Verify Installation
```bash
# Check .NET installation
dotnet --info

# Check ABP CLI version
abp --version
```

## Resources

- [Official ABP Documentation](https://docs.abp.io)
- [.NET Installation Guide](https://learn.microsoft.com/en-us/dotnet/core/install/macos)
- [ABP Framework GitHub](https://github.com/abpframework/abp)

> For persistent issues, please consult the official ABP documentation.